const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Security middleware - configure CSP to allow external images
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https://images.unsplash.com", "https://via.placeholder.com", "https://*.unsplash.com"],
            connectSrc: ["'self'", "https://api.razorpay.com"],
            frameSrc: ["'self'", "https://api.razorpay.com"]
        }
    }
}));
app.use(compression());

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nishi-scouture', {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => console.log('Connected to MongoDB'))
.catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/orders', require('./routes/orders'));
app.use('/api/admin', require('./routes/admin'));
app.use('/api/staff', require('./routes/staff'));
app.use('/api/payments', require('./routes/payments'));
app.use('/api/reviews', require('./routes/reviews'));

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ message: 'Route not found' });
});

// Basic notification service (mock for development)
const notificationService = {
    async sendEmailNotification(to, subject, html) {
        if (process.env.EMAIL_USER && process.env.EMAIL_PASS) {
            try {
                const nodemailer = require('nodemailer');
                const transporter = nodemailer.createTransporter({
                    service: 'gmail',
                    auth: {
                        user: process.env.EMAIL_USER,
                        pass: process.env.EMAIL_PASS
                    }
                });
                
                await transporter.sendMail({
                    from: process.env.EMAIL_USER,
                    to,
                    subject,
                    html
                });
                console.log('Email sent to:', to);
            } catch (error) {
                console.error('Email notification error:', error);
            }
        } else {
            console.log('Mock email notification:', { to, subject, html: html.substring(0, 100) + '...' });
        }
    },

    async sendSMSNotification(to, message) {
        if (process.env.TWILIO_SID && process.env.TWILIO_TOKEN) {
            try {
                const twilio = require('twilio');
                const client = twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);
                
                await client.messages.create({
                    body: message,
                    from: process.env.TWILIO_PHONE,
                    to
                });
                console.log('SMS sent to:', to);
            } catch (error) {
                console.error('SMS notification error:', error);
            }
        } else {
            console.log('Mock SMS notification:', { to, message });
        }
    }
};

// Export notification service
module.exports = {
    sendEmailNotification: notificationService.sendEmailNotification,
    sendSMSNotification: notificationService.sendSMSNotification
};

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log('Environment:', {
        mongodb: process.env.MONGODB_URI ? 'Configured' : 'Using default',
        razorpay: process.env.RAZORPAY_KEY_ID ? 'Configured' : 'Using mock',
        email: process.env.EMAIL_USER ? 'Configured' : 'Using mock',
        sms: process.env.TWILIO_SID ? 'Configured' : 'Using mock'
    });
});