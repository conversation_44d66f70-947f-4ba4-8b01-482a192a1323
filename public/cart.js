// Shopping Cart Functionality
class ShoppingCart {
    constructor() {
        this.items = [];
        this.init();
    }

    init() {
        this.loadCart();
        this.renderCart();
        this.setupEventListeners();
    }

    loadCart() {
        const savedCart = localStorage.getItem('shoppingCart');
        this.items = savedCart ? JSON.parse(savedCart) : [];
    }

    saveCart() {
        localStorage.setItem('shoppingCart', JSON.stringify(this.items));
        this.updateCartCount();
    }

    addItem(item) {
        const existingItem = this.items.find(cartItem => 
            cartItem.id === item.id && 
            JSON.stringify(cartItem.customization) === JSON.stringify(item.customization)
        );

        if (existingItem) {
            existingItem.quantity += item.quantity;
        } else {
            this.items.push({
                ...item,
                id: item.id || item._id,
                quantity: item.quantity || 1
            });
        }

        this.saveCart();
    }

    removeItem(index) {
        this.items.splice(index, 1);
        this.saveCart();
        this.renderCart();
    }

    updateQuantity(index, quantity) {
        if (quantity <= 0) {
            this.removeItem(index);
            return;
        }

        this.items[index].quantity = quantity;
        this.saveCart();
        this.updateSummary();
    }

    getTotal() {
        return this.items.reduce((total, item) => {
            return total + (item.price * item.quantity);
        }, 0);
    }

    renderCart() {
        const container = document.getElementById('cartItems');
        const emptyState = document.getElementById('emptyCart');
        const summary = document.getElementById('cartSummary');

        if (this.items.length === 0) {
            emptyState.style.display = 'block';
            container.style.display = 'none';
            summary.style.display = 'none';
            return;
        }

        emptyState.style.display = 'none';
        container.style.display = 'block';
        summary.style.display = 'block';

        container.innerHTML = this.items.map((item, index) => {
            let customizationHtml = '';
            if (item.customization) {
                const custom = item.customization;
                customizationHtml = `
                    <div class="customization-details">
                        <p class="customization-label"><i class="fas fa-palette"></i> Customized:</p>
                        ${custom.fabric ? `<span class="custom-tag">Fabric: ${custom.fabric}</span>` : ''}
                        ${custom.color ? `<span class="custom-tag">Color: ${custom.color}</span>` : ''}
                        ${custom.size ? `<span class="custom-tag">Size: ${custom.size}</span>` : ''}
                        ${custom.specialInstructions ? `<span class="custom-tag">Notes: ${custom.specialInstructions}</span>` : ''}
                    </div>
                `;
            }

            return `
                <div class="cart-item">
                    <img src="${item.image || '/placeholder.jpg'}" alt="${item.name}" class="item-image">
                    <div class="item-details">
                        <h3>${item.name}</h3>
                        ${customizationHtml}
                        <p class="price">₹${item.price.toFixed(2)}</p>
                    </div>
                    <div class="item-actions">
                        <div class="quantity-selector">
                            <button class="qty-btn" onclick="cart.updateQuantity(${index}, ${item.quantity - 1})">-</button>
                            <input type="number" value="${item.quantity}" min="1"
                                   onchange="cart.updateQuantity(${index}, this.value)">
                            <button class="qty-btn" onclick="cart.updateQuantity(${index}, ${item.quantity + 1})">+</button>
                        </div>
                        <div class="item-buttons">
                            <button class="edit-btn" onclick="cart.editCustomization(${index})" title="Edit Customization">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="remove-btn" onclick="cart.removeItem(${index})" title="Remove Item">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        this.updateSummary();
    }

    updateSummary() {
        const subtotal = this.getTotal();
        const shipping = subtotal > 1000 ? 0 : 50; // Free shipping over ₹1000
        const tax = subtotal * 0.18; // 18% GST
        const total = subtotal + shipping + tax;

        document.getElementById('subtotal').textContent = `₹${subtotal.toFixed(2)}`;
        document.getElementById('shipping').textContent = `₹${shipping.toFixed(2)}`;
        document.getElementById('tax').textContent = `₹${tax.toFixed(2)}`;
        document.getElementById('total').textContent = `₹${total.toFixed(2)}`;
    }

    updateCartCount() {
        const count = this.items.reduce((total, item) => total + item.quantity, 0);
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = count;
        }
    }

    setupEventListeners() {
        const checkoutBtn = document.getElementById('checkoutBtn');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => this.checkout());
        }
    }

    editCustomization(index) {
        const item = this.items[index];
        if (!item) return;

        this.showEditCustomizationModal(item, index);
    }

    showEditCustomizationModal(item, index) {
        const modal = document.createElement('div');
        modal.className = 'modal edit-customization-modal';
        modal.style.display = 'block';

        const currentCustomization = item.customization || {};

        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-edit"></i> Edit Customization - ${item.name}</h2>
                    <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
                </div>

                <div class="modal-body">
                    <div class="customization-options">
                        <div class="option-group">
                            <label>Fabric Choice:</label>
                            <select id="editFabricSelect" class="custom-select">
                                <option value="">Select Fabric (Optional)</option>
                                <option value="cotton" ${currentCustomization.fabric === 'cotton' ? 'selected' : ''}>Cotton</option>
                                <option value="silk" ${currentCustomization.fabric === 'silk' ? 'selected' : ''}>Silk</option>
                                <option value="chiffon" ${currentCustomization.fabric === 'chiffon' ? 'selected' : ''}>Chiffon</option>
                                <option value="georgette" ${currentCustomization.fabric === 'georgette' ? 'selected' : ''}>Georgette</option>
                                <option value="crepe" ${currentCustomization.fabric === 'crepe' ? 'selected' : ''}>Crepe</option>
                                <option value="linen" ${currentCustomization.fabric === 'linen' ? 'selected' : ''}>Linen</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label>Color Preference:</label>
                            <select id="editColorSelect" class="custom-select">
                                <option value="">Select Color (Optional)</option>
                                <option value="red" ${currentCustomization.color === 'red' ? 'selected' : ''}>Red</option>
                                <option value="blue" ${currentCustomization.color === 'blue' ? 'selected' : ''}>Blue</option>
                                <option value="green" ${currentCustomization.color === 'green' ? 'selected' : ''}>Green</option>
                                <option value="pink" ${currentCustomization.color === 'pink' ? 'selected' : ''}>Pink</option>
                                <option value="black" ${currentCustomization.color === 'black' ? 'selected' : ''}>Black</option>
                                <option value="white" ${currentCustomization.color === 'white' ? 'selected' : ''}>White</option>
                                <option value="yellow" ${currentCustomization.color === 'yellow' ? 'selected' : ''}>Yellow</option>
                                <option value="purple" ${currentCustomization.color === 'purple' ? 'selected' : ''}>Purple</option>
                                <option value="orange" ${currentCustomization.color === 'orange' ? 'selected' : ''}>Orange</option>
                                <option value="custom" ${!['red','blue','green','pink','black','white','yellow','purple','orange'].includes(currentCustomization.color) && currentCustomization.color ? 'selected' : ''}>Custom Color</option>
                            </select>
                            <input type="text" id="editCustomColor" placeholder="Specify custom color"
                                   value="${!['red','blue','green','pink','black','white','yellow','purple','orange'].includes(currentCustomization.color) && currentCustomization.color ? currentCustomization.color : ''}"
                                   style="display: ${!['red','blue','green','pink','black','white','yellow','purple','orange'].includes(currentCustomization.color) && currentCustomization.color ? 'block' : 'none'}; margin-top: 10px;" class="custom-input">
                        </div>

                        <div class="option-group">
                            <label>Size:</label>
                            <select id="editSizeSelect" class="custom-select">
                                <option value="">Select Size (Optional)</option>
                                <option value="XS" ${currentCustomization.size === 'XS' ? 'selected' : ''}>XS</option>
                                <option value="S" ${currentCustomization.size === 'S' ? 'selected' : ''}>S</option>
                                <option value="M" ${currentCustomization.size === 'M' ? 'selected' : ''}>M</option>
                                <option value="L" ${currentCustomization.size === 'L' ? 'selected' : ''}>L</option>
                                <option value="XL" ${currentCustomization.size === 'XL' ? 'selected' : ''}>XL</option>
                                <option value="XXL" ${currentCustomization.size === 'XXL' ? 'selected' : ''}>XXL</option>
                                <option value="custom" ${currentCustomization.size === 'custom' ? 'selected' : ''}>Custom Measurements</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label>Special Instructions:</label>
                            <textarea id="editSpecialInstructions" placeholder="Any special requests or modifications..."
                                      class="custom-textarea" rows="3">${currentCustomization.specialInstructions || ''}</textarea>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
                        Cancel
                    </button>
                    <button class="btn btn-danger" onclick="cart.removeCustomization(${index}); this.closest('.modal').remove()">
                        <i class="fas fa-times"></i> Remove Customization
                    </button>
                    <button class="btn btn-primary" onclick="cart.saveCustomization(${index}); this.closest('.modal').remove()">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Setup color select change handler
        const colorSelect = document.getElementById('editColorSelect');
        const customColorInput = document.getElementById('editCustomColor');

        colorSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customColorInput.style.display = 'block';
            } else {
                customColorInput.style.display = 'none';
            }
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    saveCustomization(index) {
        const fabric = document.getElementById('editFabricSelect').value;
        const color = document.getElementById('editColorSelect').value;
        const customColor = document.getElementById('editCustomColor').value;
        const size = document.getElementById('editSizeSelect').value;
        const instructions = document.getElementById('editSpecialInstructions').value;

        const customization = {
            fabric: fabric || null,
            color: color === 'custom' ? customColor : color || null,
            size: size || null,
            specialInstructions: instructions || null
        };

        // Only include customization if at least one option is selected
        const hasCustomization = Object.values(customization).some(value => value);

        this.items[index].customization = hasCustomization ? customization : null;
        this.saveCart();
        this.renderCart();

        // Show success message
        this.showMessage('Customization updated successfully!', 'success');
    }

    removeCustomization(index) {
        this.items[index].customization = null;
        this.saveCart();
        this.renderCart();

        // Show success message
        this.showMessage('Customization removed successfully!', 'success');
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `cart-message ${type}`;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    checkout() {
        if (this.items.length === 0) {
            alert('Your cart is empty!');
            return;
        }

        // Save cart for checkout
        const checkoutData = {
            items: this.items,
            subtotal: this.getTotal(),
            shipping: this.getTotal() > 1000 ? 0 : 50,
            tax: this.getTotal() * 0.18,
            total: this.getTotal() + (this.getTotal() > 1000 ? 0 : 50) + (this.getTotal() * 0.18)
        };

        sessionStorage.setItem('checkoutCart', JSON.stringify(checkoutData));
        window.location.href = 'checkout.html';
    }
}

// Initialize cart
const cart = new ShoppingCart();

// Update cart count on page load
cart.updateCartCount();