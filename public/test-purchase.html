<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        input, select { margin: 5px; padding: 8px; }
    </style>
</head>
<body>
    <h1>Purchase Flow Diagnostic Test</h1>
    
    <div class="test-section">
        <h3>1. Authentication Check</h3>
        <div id="authResults"></div>
        <button onclick="testAuth()">Test Authentication</button>
    </div>

    <div class="test-section">
        <h3>2. Cart Functionality</h3>
        <div>
            <button onclick="testAddToCart()">Test Add to Cart</button>
            <button onclick="testCartLoad()">Test Cart Load</button>
            <button onclick="clearCart()">Clear Cart</button>
        </div>
        <div id="cartResults"></div>
    </div>

    <div class="test-section">
        <h3>3. Order Creation Test</h3>
        <div>
            <input type="text" id="customerName" placeholder="Customer Name" value="Test User">
            <input type="email" id="customerEmail" placeholder="Email" value="<EMAIL>">
            <input type="text" id="customerPhone" placeholder="Phone" value="1234567890">
            <br>
            <input type="text" id="address" placeholder="Address" value="123 Test Street">
            <input type="text" id="city" placeholder="City" value="Test City">
            <input type="text" id="state" placeholder="State" value="Test State">
            <input type="text" id="zipCode" placeholder="ZIP" value="12345">
            <br>
            <button onclick="testOrderCreation()">Test Order Creation</button>
        </div>
        <div id="orderResults"></div>
    </div>

    <div class="test-section">
        <h3>4. Payment Flow Test</h3>
        <div>
            <input type="text" id="testOrderId" placeholder="Order ID (from step 3)">
            <button onclick="testPaymentCreation()">Test Payment Creation</button>
        </div>
        <div id="paymentResults"></div>
    </div>

    <div class="test-section">
        <h3>5. Full Purchase Flow Test</h3>
        <div>
            <button onclick="testFullFlow()">Test Complete Purchase Flow</button>
        </div>
        <div id="fullFlowResults"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            element.innerHTML += `<div class="${className}">${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function clearResults(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function testAuth() {
            clearResults('authResults');
            
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (!token) {
                log('authResults', 'No token found! Please login first.', 'error');
                return false;
            }

            try {
                const response = await fetch('/api/auth/validate', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('authResults', `✅ Authentication valid - User: ${data.user.email} (${data.user.role})`, 'success');
                    return true;
                } else {
                    const error = await response.json();
                    log('authResults', `❌ Authentication failed: ${error.error}`, 'error');
                    return false;
                }
            } catch (error) {
                log('authResults', `❌ Auth test error: ${error.message}`, 'error');
                return false;
            }
        }

        function testAddToCart() {
            clearResults('cartResults');
            
            try {
                // Test adding a sample item to cart
                const testItem = {
                    id: 'test-dress-1',
                    name: 'Test Dress',
                    price: 1000,
                    image: 'test.jpg',
                    quantity: 1,
                    customization: {
                        fabric: 'Cotton',
                        color: 'Blue'
                    }
                };

                // Simulate cart functionality
                let cart = JSON.parse(localStorage.getItem('shoppingCart') || '[]');
                cart.push(testItem);
                localStorage.setItem('shoppingCart', JSON.stringify(cart));
                
                log('cartResults', '✅ Test item added to cart successfully', 'success');
                log('cartResults', `Cart now has ${cart.length} items`, 'info');
            } catch (error) {
                log('cartResults', `❌ Add to cart failed: ${error.message}`, 'error');
            }
        }

        function testCartLoad() {
            clearResults('cartResults');
            
            try {
                const cart = JSON.parse(localStorage.getItem('shoppingCart') || '[]');
                log('cartResults', `✅ Cart loaded successfully - ${cart.length} items`, 'success');
                
                if (cart.length > 0) {
                    cart.forEach((item, index) => {
                        log('cartResults', `Item ${index + 1}: ${item.name} - ₹${item.price}`, 'info');
                    });
                }
            } catch (error) {
                log('cartResults', `❌ Cart load failed: ${error.message}`, 'error');
            }
        }

        function clearCart() {
            localStorage.removeItem('shoppingCart');
            log('cartResults', '🗑️ Cart cleared', 'warning');
        }

        async function testOrderCreation() {
            clearResults('orderResults');
            
            const authValid = await testAuth();
            if (!authValid) {
                log('orderResults', '❌ Cannot test order creation - authentication failed', 'error');
                return;
            }

            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            const cart = JSON.parse(localStorage.getItem('shoppingCart') || '[]');
            
            if (cart.length === 0) {
                log('orderResults', '❌ Cart is empty! Add items first.', 'error');
                return;
            }

            const orderData = {
                items: cart,
                customerInfo: {
                    fullName: document.getElementById('customerName').value,
                    email: document.getElementById('customerEmail').value,
                    phone: document.getElementById('customerPhone').value,
                    address: document.getElementById('address').value,
                    city: document.getElementById('city').value,
                    state: document.getElementById('state').value,
                    zipCode: document.getElementById('zipCode').value,
                    country: 'India'
                },
                totalAmount: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
                shippingAddress: {
                    fullName: document.getElementById('customerName').value,
                    address: document.getElementById('address').value,
                    city: document.getElementById('city').value,
                    state: document.getElementById('state').value,
                    zipCode: document.getElementById('zipCode').value,
                    country: 'India'
                },
                notes: 'Test order'
            };

            try {
                const response = await fetch('/api/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(orderData)
                });

                if (response.ok) {
                    const result = await response.json();
                    log('orderResults', `✅ Order created successfully!`, 'success');
                    log('orderResults', `Order ID: ${result.order.orderId}`, 'info');
                    log('orderResults', `Total: ₹${result.order.totalAmount}`, 'info');
                    document.getElementById('testOrderId').value = result.order._id;
                } else {
                    const error = await response.json();
                    log('orderResults', `❌ Order creation failed: ${error.message}`, 'error');
                }
            } catch (error) {
                log('orderResults', `❌ Order creation error: ${error.message}`, 'error');
            }
        }

        async function testPaymentCreation() {
            clearResults('paymentResults');
            
            const orderId = document.getElementById('testOrderId').value;
            if (!orderId) {
                log('paymentResults', '❌ Please create an order first', 'error');
                return;
            }

            const token = localStorage.getItem('token') || sessionStorage.getItem('token');

            try {
                const response = await fetch('/api/payments/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ orderId: orderId })
                });

                if (response.ok) {
                    const result = await response.json();
                    log('paymentResults', '✅ Payment order created successfully!', 'success');
                    log('paymentResults', `Razorpay Order ID: ${result.order.id}`, 'info');
                    log('paymentResults', `Amount: ₹${result.order.amount / 100}`, 'info');
                } else {
                    const error = await response.json();
                    log('paymentResults', `❌ Payment creation failed: ${error.message}`, 'error');
                }
            } catch (error) {
                log('paymentResults', `❌ Payment creation error: ${error.message}`, 'error');
            }
        }

        async function testFullFlow() {
            clearResults('fullFlowResults');
            log('fullFlowResults', '🚀 Starting full purchase flow test...', 'info');
            
            // Step 1: Auth check
            const authValid = await testAuth();
            if (!authValid) {
                log('fullFlowResults', '❌ Full flow failed at authentication', 'error');
                return;
            }
            log('fullFlowResults', '✅ Step 1: Authentication passed', 'success');

            // Step 2: Add to cart
            testAddToCart();
            log('fullFlowResults', '✅ Step 2: Item added to cart', 'success');

            // Step 3: Create order
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait a bit
            await testOrderCreation();
            
            const orderId = document.getElementById('testOrderId').value;
            if (!orderId) {
                log('fullFlowResults', '❌ Full flow failed at order creation', 'error');
                return;
            }
            log('fullFlowResults', '✅ Step 3: Order created successfully', 'success');

            // Step 4: Create payment
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait a bit
            await testPaymentCreation();
            log('fullFlowResults', '✅ Step 4: Payment order created', 'success');

            log('fullFlowResults', '🎉 Full purchase flow test completed successfully!', 'success');
        }

        // Initialize
        window.onload = function() {
            testAuth();
            testCartLoad();
        };
    </script>
</body>
</html>
