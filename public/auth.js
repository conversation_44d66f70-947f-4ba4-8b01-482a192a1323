// Authentication JavaScript
class AuthManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
    }

    bindEvents() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }
    }

    checkAuthStatus() {
        // Don't run auth checks on customize page - let the page handle its own auth
        if (window.location.pathname.includes('customize.html')) {
            console.log('Auth.js: Skipping auth check on customize page');
            return;
        }

        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        if (token) {
            // Check if token is valid
            this.validateToken(token);
        }
    }

    async validateToken(token) {
        try {
            const response = await fetch('/api/auth/validate', {
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Token validation successful:', data.user);

                // Store user data if not already stored
                const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');
                if (!storedUser) {
                    if (localStorage.getItem('token')) {
                        localStorage.setItem('user', JSON.stringify(data.user));
                    } else {
                        sessionStorage.setItem('user', JSON.stringify(data.user));
                    }
                }

                this.redirectBasedOnRole(data.user.role);
            } else {
                console.log('Token validation failed, clearing tokens');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                sessionStorage.removeItem('token');
                sessionStorage.removeItem('user');
            }
        } catch (error) {
            console.error('Token validation error:', error);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            sessionStorage.removeItem('token');
            sessionStorage.removeItem('user');
        }
    }

    redirectBasedOnRole(role) {
        if (window.location.pathname === '/login.html' || window.location.pathname === '/register.html') {
            if (role === 'customer') {
                window.location.href = '/profile.html';
            } else if (role === 'staff' || role === 'admin') {
                window.location.href = '/manager.html';
            }
        }
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(`/api/auth${endpoint}`, options);
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || 'API call failed');
        }

        return result;
    }

    async handleLogin(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const rememberMe = formData.get('rememberMe') || document.getElementById('rememberMe')?.checked;
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        // Show loading state
        const loginBtn = document.getElementById('loginBtn') || document.querySelector('button[type="submit"]');
        const btnText = loginBtn?.querySelector('.btn-text');
        const spinner = loginBtn?.querySelector('.loading-spinner') || loginBtn?.querySelector('.spinner');

        if (loginBtn) {
            loginBtn.disabled = true;
            if (btnText) btnText.style.display = 'none';
            if (spinner) spinner.style.display = 'inline-block';
        }

        try {
            const response = await this.apiCall('/login', 'POST', loginData);

            // Store token based on remember me preference
            if (rememberMe) {
                localStorage.setItem('token', response.token);
                localStorage.setItem('user', JSON.stringify(response.user));
            } else {
                sessionStorage.setItem('token', response.token);
                sessionStorage.setItem('user', JSON.stringify(response.user));
            }

            this.showNotification('Login successful! Redirecting...', 'success');

            // Check for pending customization
            const pendingCustomization = sessionStorage.getItem('pendingCustomization');

            // Redirect based on role or pending action
            setTimeout(() => {
                if (pendingCustomization) {
                    sessionStorage.removeItem('pendingCustomization');
                    window.location.href = `index.html?customize=${pendingCustomization}`;
                } else {
                    this.redirectBasedOnRole(response.user.role);
                }
            }, 1500);

        } catch (error) {
            console.error('Login error:', error);
            this.showError(error.message);
        } finally {
            // Reset button state
            if (loginBtn) {
                loginBtn.disabled = false;
                if (btnText) btnText.style.display = 'inline';
                if (spinner) spinner.style.display = 'none';
            }
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        
        // Validate passwords match
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        
        if (password !== confirmPassword) {
            this.showError('Passwords do not match');
            return;
        }

        const registerData = {
            fullName: formData.get('fullName'),
            email: formData.get('email'),
            phoneNumber: formData.get('phoneNumber'),
            password: password
        };

        try {
            const response = await this.apiCall('/register', 'POST', registerData);
            
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
            
            this.showNotification('Registration successful!', 'success');
            
            // Redirect to profile
            setTimeout(() => {
                window.location.href = '/profile.html';
            }, 1000);
            
        } catch (error) {
            console.error('Registration error:', error);
            this.showError(error.message);
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user');
        console.log('User logged out, all tokens cleared');
        window.location.href = '/login.html';
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Make authManager globally accessible
window.authManager = authManager;

// Global logout function
function logout() {
    authManager.logout();
}