// Authentication JavaScript
class AuthManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAuthStatus();
    }

    bindEvents() {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }
    }

    checkAuthStatus() {
        // Don't run auth checks on customize page - let the page handle its own auth
        if (window.location.pathname.includes('customize.html')) {
            console.log('Auth.js: Skipping auth check on customize page');
            return;
        }

        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        if (token) {
            // Check if token is valid
            this.validateToken(token);
        }
    }

    async validateToken(token) {
        try {
            const response = await fetch('/api/auth/validate', {
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.redirectBasedOnRole(data.user.role);
            } else {
                localStorage.removeItem('token');
                sessionStorage.removeItem('token');
            }
        } catch (error) {
            console.error('Token validation error:', error);
            localStorage.removeItem('token');
            sessionStorage.removeItem('token');
        }
    }

    redirectBasedOnRole(role) {
        if (window.location.pathname === '/login.html' || window.location.pathname === '/register.html') {
            if (role === 'customer') {
                window.location.href = '/profile.html';
            } else if (role === 'staff' || role === 'admin') {
                window.location.href = '/manager.html';
            }
        }
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(`/api/auth${endpoint}`, options);
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || 'API call failed');
        }

        return result;
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        try {
            const response = await this.apiCall('/login', 'POST', loginData);
            
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
            
            this.showNotification('Login successful!', 'success');
            
            // Redirect based on role
            setTimeout(() => {
                this.redirectBasedOnRole(response.user.role);
            }, 1000);
            
        } catch (error) {
            console.error('Login error:', error);
            this.showError(error.message);
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        
        // Validate passwords match
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        
        if (password !== confirmPassword) {
            this.showError('Passwords do not match');
            return;
        }

        const registerData = {
            fullName: formData.get('fullName'),
            email: formData.get('email'),
            phoneNumber: formData.get('phoneNumber'),
            password: password
        };

        try {
            const response = await this.apiCall('/register', 'POST', registerData);
            
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
            
            this.showNotification('Registration successful!', 'success');
            
            // Redirect to profile
            setTimeout(() => {
                window.location.href = '/profile.html';
            }, 1000);
            
        } catch (error) {
            console.error('Registration error:', error);
            this.showError(error.message);
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login.html';
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Global logout function
function logout() {
    authManager.logout();
}