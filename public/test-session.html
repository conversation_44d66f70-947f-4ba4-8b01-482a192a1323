<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Management Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        .token-display { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; word-break: break-all; }
    </style>
</head>
<body>
    <h1>Session Management Test</h1>
    
    <div class="test-section">
        <h3>Current Session Status</h3>
        <div id="sessionStatus"></div>
        <button onclick="checkSession()">Check Session</button>
        <button onclick="clearSession()">Clear Session</button>
    </div>

    <div class="test-section">
        <h3>Token Storage Test</h3>
        <div>
            <button onclick="testLocalStorage()">Test localStorage</button>
            <button onclick="testSessionStorage()">Test sessionStorage</button>
        </div>
        <div id="storageResults"></div>
    </div>

    <div class="test-section">
        <h3>API Validation Test</h3>
        <div>
            <input type="text" id="testToken" placeholder="Enter token to test" style="width: 300px;">
            <button onclick="testTokenValidation()">Test Token</button>
        </div>
        <div id="validationResults"></div>
    </div>

    <div class="test-section">
        <h3>Login Test</h3>
        <div>
            <input type="email" id="testEmail" placeholder="Email" style="width: 200px;">
            <input type="password" id="testPassword" placeholder="Password" style="width: 200px;">
            <label><input type="checkbox" id="testRememberMe"> Remember Me</label>
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div id="loginResults"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<div class="${className}">${new Date().toLocaleTimeString()}: ${message}</div>`;
        }

        function checkSession() {
            const localToken = localStorage.getItem('token');
            const sessionToken = sessionStorage.getItem('token');
            const localUser = localStorage.getItem('user');
            const sessionUser = sessionStorage.getItem('user');

            let status = '<h4>Current Session:</h4>';
            status += `<div><strong>localStorage token:</strong> ${localToken ? 'EXISTS' : 'NONE'}</div>`;
            status += `<div><strong>sessionStorage token:</strong> ${sessionToken ? 'EXISTS' : 'NONE'}</div>`;
            status += `<div><strong>localStorage user:</strong> ${localUser ? 'EXISTS' : 'NONE'}</div>`;
            status += `<div><strong>sessionStorage user:</strong> ${sessionUser ? 'EXISTS' : 'NONE'}</div>`;

            if (localToken) {
                status += `<div class="token-display"><strong>localStorage token:</strong><br>${localToken}</div>`;
            }
            if (sessionToken) {
                status += `<div class="token-display"><strong>sessionStorage token:</strong><br>${sessionToken}</div>`;
            }

            document.getElementById('sessionStatus').innerHTML = status;
        }

        function clearSession() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            sessionStorage.removeItem('token');
            sessionStorage.removeItem('user');
            log('sessionStatus', 'All session data cleared', 'success');
            checkSession();
        }

        function testLocalStorage() {
            try {
                const testData = 'test-token-' + Date.now();
                localStorage.setItem('testToken', testData);
                const retrieved = localStorage.getItem('testToken');
                localStorage.removeItem('testToken');
                
                if (retrieved === testData) {
                    log('storageResults', 'localStorage working correctly', 'success');
                } else {
                    log('storageResults', 'localStorage test failed', 'error');
                }
            } catch (error) {
                log('storageResults', `localStorage error: ${error.message}`, 'error');
            }
        }

        function testSessionStorage() {
            try {
                const testData = 'test-session-token-' + Date.now();
                sessionStorage.setItem('testSessionToken', testData);
                const retrieved = sessionStorage.getItem('testSessionToken');
                sessionStorage.removeItem('testSessionToken');
                
                if (retrieved === testData) {
                    log('storageResults', 'sessionStorage working correctly', 'success');
                } else {
                    log('storageResults', 'sessionStorage test failed', 'error');
                }
            } catch (error) {
                log('storageResults', `sessionStorage error: ${error.message}`, 'error');
            }
        }

        async function testTokenValidation() {
            const token = document.getElementById('testToken').value;
            if (!token) {
                log('validationResults', 'Please enter a token to test', 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/validate', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('validationResults', `Token valid! User: ${data.user.email} (${data.user.role})`, 'success');
                } else {
                    const error = await response.json();
                    log('validationResults', `Token invalid: ${error.error}`, 'error');
                }
            } catch (error) {
                log('validationResults', `Validation error: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            const rememberMe = document.getElementById('testRememberMe').checked;

            if (!email || !password) {
                log('loginResults', 'Please enter email and password', 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    // Store token based on remember me
                    if (rememberMe) {
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        log('loginResults', 'Login successful! Token stored in localStorage', 'success');
                    } else {
                        sessionStorage.setItem('token', data.token);
                        sessionStorage.setItem('user', JSON.stringify(data.user));
                        log('loginResults', 'Login successful! Token stored in sessionStorage', 'success');
                    }
                    
                    log('loginResults', `User: ${data.user.email} (${data.user.role})`, 'info');
                    checkSession();
                } else {
                    log('loginResults', `Login failed: ${data.error}`, 'error');
                }
            } catch (error) {
                log('loginResults', `Login error: ${error.message}`, 'error');
            }
        }

        // Initialize
        checkSession();
    </script>
</body>
</html>
