// Checkout Page Functionality
class Checkout {
    constructor() {
        this.cartData = null;
        this.init();
    }

    init() {
        this.loadCartData();
        this.renderOrderSummary();
        this.setupEventListeners();
    }

    loadCartData() {
        const cartData = sessionStorage.getItem('checkoutCart');
        if (!cartData) {
            window.location.href = 'cart.html';
            return;
        }

        this.cartData = JSON.parse(cartData);
    }

    renderOrderSummary() {
        const itemsContainer = document.getElementById('orderItems');
        
        itemsContainer.innerHTML = this.cartData.items.map(item => `
            <div class="checkout-item">
                <img src="${item.image || '/placeholder.jpg'}" alt="${item.name}" class="item-image">
                <div class="item-info">
                    <h4>${item.name}</h4>
                    ${item.customization ? `<p class="customization">${item.customization}</p>` : ''}
                    <p class="quantity">Qty: ${item.quantity}</p>
                </div>
                <div class="item-price">
                    ₹${(item.price * item.quantity).toFixed(2)}
                </div>
            </div>
        `).join('');

        // Update totals
        document.getElementById('subtotal').textContent = `₹${this.cartData.subtotal.toFixed(2)}`;
        document.getElementById('shipping').textContent = `₹${this.cartData.shipping.toFixed(2)}`;
        document.getElementById('tax').textContent = `₹${this.cartData.tax.toFixed(2)}`;
        document.getElementById('total').textContent = `₹${this.cartData.total.toFixed(2)}`;
    }

    setupEventListeners() {
        const form = document.getElementById('checkoutForm');
        form.addEventListener('submit', (e) => this.handleCheckout(e));
    }

    async handleCheckout(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const customerData = Object.fromEntries(formData);
        
        // Validate form
        if (!this.validateForm(customerData)) {
            return;
        }

        // Show loading
        this.setLoading(true);

        try {
            // Create order
            const orderData = await this.createOrder(customerData);
            
            // Initialize Razorpay payment
            await this.initiatePayment(orderData);
            
        } catch (error) {
            console.error('Checkout error:', error);
            this.showError(error.message || 'Checkout failed. Please try again.');
        } finally {
            this.setLoading(false);
        }
    }

    validateForm(data) {
        const required = ['fullName', 'email', 'phone', 'address', 'city', 'state', 'zipCode'];
        
        for (const field of required) {
            if (!data[field] || data[field].trim() === '') {
                this.showError(`${field.replace(/([A-Z])/g, ' $1').toUpperCase()} is required`);
                return false;
            }
        }

        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            this.showError('Please enter a valid email address');
            return false;
        }

        // Validate phone
        const phoneRegex = /^[0-9]{10}$/;
        if (!phoneRegex.test(data.phone)) {
            this.showError('Please enter a valid 10-digit phone number');
            return false;
        }

        return true;
    }

    async createOrder(customerData) {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        
        const orderData = {
            items: this.cartData.items,
            customerInfo: customerData,
            totalAmount: this.cartData.total,
            shippingAddress: {
                fullName: customerData.fullName,
                address: customerData.address,
                city: customerData.city,
                state: customerData.state,
                zipCode: customerData.zipCode,
                country: customerData.country || 'India'
            },
            notes: customerData.notes || ''
        };

        const response = await fetch('/api/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to create order');
        }

        return response.json();
    }

    async initiatePayment(orderData) {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        
        // Create Razorpay order
        const razorpayResponse = await fetch('/api/payments/create-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ orderId: orderData.order._id })
        });

        if (!razorpayResponse.ok) {
            throw new Error('Failed to create payment order');
        }

        const { order } = await razorpayResponse.json();

        // Configure Razorpay
        const options = {
            key: order.key,
            amount: order.amount,
            currency: order.currency,
            name: 'Nishi\'s Couture',
            description: 'Custom Dress Order',
            order_id: order.id,
            handler: async (response) => {
                await this.verifyPayment(response, orderData.order._id);
            },
            prefill: {
                name: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                contact: document.getElementById('phone').value
            },
            theme: {
                color: '#ff6b6b'
            },
            modal: {
                ondismiss: () => {
                    this.showError('Payment was cancelled. Please try again.');
                    this.setLoading(false);
                }
            }
        };

        // Check if Razorpay is loaded
        if (typeof Razorpay === 'undefined') {
            throw new Error('Razorpay SDK not loaded. Please check your internet connection.');
        }

        const razorpay = new Razorpay(options);
        razorpay.open();
    }

    async verifyPayment(paymentResponse, orderId) {
        try {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            
            const response = await fetch('/api/payments/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    razorpay_order_id: paymentResponse.razorpay_order_id,
                    razorpay_payment_id: paymentResponse.razorpay_payment_id,
                    razorpay_signature: paymentResponse.razorpay_signature
                })
            });

            if (!response.ok) {
                throw new Error('Payment verification failed');
            }

            const { order } = await response.json();
            
            // Clear cart
            if (window.cart) {
                window.cart.clearCart();
            }
            
            // Store order for success page
            sessionStorage.setItem('orderSuccess', JSON.stringify(order));
            
            // Redirect to success page
            window.location.href = 'order-success.html';
            
        } catch (error) {
            console.error('Payment verification error:', error);
            this.showError(`Payment verification failed: ${error.message}. Please contact support if the issue persists.`);
            this.setLoading(false);
        }
    }

    setLoading(loading) {
        const submitBtn = document.querySelector('button[type="submit"]');
        const text = document.getElementById('checkoutText');
        const spinner = document.getElementById('checkoutSpinner');

        if (loading) {
            submitBtn.disabled = true;
            text.style.display = 'none';
            spinner.style.display = 'inline-block';
        } else {
            submitBtn.disabled = false;
            text.style.display = 'inline';
            spinner.style.display = 'none';
        }
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

// Initialize checkout
document.addEventListener('DOMContentLoaded', () => {
    new Checkout();
});