<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Authentication Flow Test</h1>
    
    <div class="test-section">
        <h2>Storage Key Consistency Test</h2>
        <button onclick="testStorageKeys()">Test Storage Keys</button>
        <div id="storage-test" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Token Storage Test</h2>
        <button onclick="testTokenStorage()">Test Token Storage</button>
        <div id="token-test" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>User Data Storage Test</h2>
        <button onclick="testUserDataStorage()">Test User Data Storage</button>
        <div id="user-test" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Clear Storage</h2>
        <button onclick="clearStorage()">Clear All Storage</button>
    </div>

    <div class="test-section">
        <h2>Quick Navigation</h2>
        <button onclick="window.location.href='/'">Home</button>
        <button onclick="window.location.href='login.html'">Login</button>
        <button onclick="window.location.href='register.html'">Register</button>
        <button onclick="window.location.href='profile.html'">Profile</button>
    </div>

    <script>
        function testStorageKeys() {
            const result = document.getElementById('storage-test');
            const keys = {
                'token': localStorage.getItem('token') || sessionStorage.getItem('token'),
                'user': localStorage.getItem('user') || sessionStorage.getItem('user'),
                'userData': localStorage.getItem('userData') || sessionStorage.getItem('userData')
            };
            
            let html = '<h3>Storage Keys Found:</h3>';
            Object.keys(keys).forEach(key => {
                html += `<div>${key}: ${keys[key] ? '✅ Found' : '❌ Not found'}</div>`;
            });
            
            if (keys.userData && !keys.user) {
                html += '<div class="error">⚠️ Found userData but no user key - potential inconsistency!</div>';
            }
            
            result.innerHTML = html;
            result.className = 'test-result info';
        }
        
        function testTokenStorage() {
            const result = document.getElementById('token-test');
            
            // Test setting token
            const testToken = 'test-token-' + Date.now();
            localStorage.setItem('token', testToken);
            
            // Test retrieving token
            const retrieved = localStorage.getItem('token');
            
            if (retrieved === testToken) {
                result.innerHTML = `<div class="success">✅ Token storage working correctly</div>`;
                result.className = 'test-result success';
            } else {
                result.innerHTML = `<div class="error">❌ Token storage issue</div>`;
                result.className = 'test-result error';
            }
            
            // Clean up
            localStorage.removeItem('token');
        }
        
        function testUserDataStorage() {
            const result = document.getElementById('user-test');
            
            // Test setting user data
            const testUser = { name: 'Test User', email: '<EMAIL>' };
            localStorage.setItem('user', JSON.stringify(testUser));
            
            // Test retrieving user data
            const retrieved = JSON.parse(localStorage.getItem('user') || '{}');
            
            if (retrieved.name === testUser.name) {
                result.innerHTML = `<div class="success">✅ User data storage working correctly</div>`;
                result.className = 'test-result success';
            } else {
                result.innerHTML = `<div class="error">❌ User data storage issue</div>`;
                result.className = 'test-result error';
            }
            
            // Clean up
            localStorage.removeItem('user');
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            alert('Storage cleared!');
            location.reload();
        }
        
        // Run tests on page load
        window.addEventListener('load', () => {
            testStorageKeys();
        });
    </script>
</body>
</html>