<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customize Your Dress - <PERSON><PERSON>'s Couture</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><PERSON><PERSON>'s Couture</h1>
                <p>Premium Custom Tailoring</p>
            </div>
            <nav class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="index.html#dresses" class="nav-link">Dresses</a>
                <a href="profile.html" class="nav-link">Profile</a>
                <button id="logout-btn" class="btn-login">Logout</button>
            </nav>
            <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <div class="mobile-nav" id="mobile-nav">
        <div class="mobile-nav-header">
            <h3>Nishi's Couture</h3>
            <span class="mobile-nav-close" onclick="toggleMobileMenu()">&times;</span>
        </div>
        <div class="mobile-nav-links">
            <a href="index.html">Home</a>
            <a href="index.html#dresses">Dresses</a>
            <a href="profile.html">Profile</a>
            <a href="#" onclick="logout()">Logout</a>
        </div>
    </div>
    <div class="mobile-overlay" id="mobile-overlay" onclick="closeMobileMenu()"></div>

    <!-- Main Content -->
    <main class="customize-main">
        <div class="container">
            <!-- Hero Section -->
            <div class="customize-hero">
                <div class="hero-content">
                    <h1 class="hero-title">Customize Your Perfect Dress</h1>
                    <p class="hero-subtitle">Experience the art of bespoke tailoring with our guided customization process</p>
                </div>

                <!-- Enhanced Progress Indicator -->
                <div class="progress-container">
                    <div class="progress-steps">
                        <div class="progress-step active" data-step="1">
                            <div class="step-circle">
                                <i class="fas fa-eye"></i>
                                <span class="step-number">1</span>
                            </div>
                            <div class="step-info">
                                <span class="step-title">Preview</span>
                                <span class="step-desc">View your selection</span>
                            </div>
                        </div>
                        <div class="progress-step" data-step="2">
                            <div class="step-circle">
                                <i class="fas fa-cut"></i>
                                <span class="step-number">2</span>
                            </div>
                            <div class="step-info">
                                <span class="step-title">Fabric</span>
                                <span class="step-desc">Choose material</span>
                            </div>
                        </div>
                        <div class="progress-step" data-step="3">
                            <div class="step-circle">
                                <i class="fas fa-palette"></i>
                                <span class="step-number">3</span>
                            </div>
                            <div class="step-info">
                                <span class="step-title">Color</span>
                                <span class="step-desc">Select shade</span>
                            </div>
                        </div>
                        <div class="progress-step" data-step="4">
                            <div class="step-circle">
                                <i class="fas fa-ruler"></i>
                                <span class="step-number">4</span>
                            </div>
                            <div class="step-info">
                                <span class="step-title">Measurements</span>
                                <span class="step-desc">Perfect fit</span>
                            </div>
                        </div>
                        <div class="progress-step" data-step="4">
                            <div class="step-circle">
                                <i class="fas fa-truck"></i>
                                <span class="step-number">4</span>
                            </div>
                            <div class="step-info">
                                <span class="step-title">Delivery</span>
                                <span class="step-desc">Final details</span>
                            </div>
                        </div>
                    </div>
                    <div class="progress-line">
                        <div class="progress-fill" style="width: 25%"></div>
                    </div>
                </div>
            </div>

            <!-- Main Customization Layout -->
            <div class="customize-layout">
                <!-- Left Panel - Dress Preview -->
                <div class="preview-panel">
                    <div class="preview-card">
                        <div class="preview-header">
                            <h2>Your Design</h2>
                            <div class="preview-badges">
                                <span class="badge premium">
                                    <i class="fas fa-crown"></i>
                                    Premium
                                </span>
                                <span class="badge custom">
                                    <i class="fas fa-magic"></i>
                                    Custom Fit
                                </span>
                            </div>
                        </div>

                        <div class="dress-showcase">
                            <div class="dress-image-wrapper">
                                <img id="dress-image" src="" alt="Selected Dress" loading="eager">
                                <div class="image-overlay">
                                    <button class="zoom-btn" onclick="zoomImage()">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="dress-details">
                                <h3 id="dress-name" class="dress-title"></h3>
                                <p id="dress-description" class="dress-desc"></p>

                                <div class="customization-summary">
                                    <h4>Your Selections</h4>
                                    <div class="selection-grid">
                                        <div class="selection-item">
                                            <span class="selection-label">Fabric:</span>
                                            <span class="selection-value" id="selected-fabric">Not selected</span>
                                        </div>
                                        <div class="selection-item">
                                            <span class="selection-label">Color:</span>
                                            <span class="selection-value" id="selected-color">Not selected</span>
                                        </div>
                                        <div class="selection-item">
                                            <span class="selection-label">Size:</span>
                                            <span class="selection-value" id="selected-size">Not selected</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="price-summary">
                                    <div class="price-breakdown">
                                        <div class="price-item">
                                            <span>Base Price:</span>
                                            <span>₹<span id="base-price">0</span></span>
                                        </div>
                                        <div class="price-item">
                                            <span>Customization:</span>
                                            <span>₹<span id="custom-price">0</span></span>
                                        </div>
                                        <div class="price-total">
                                            <span>Total:</span>
                                            <span>₹<span id="total-price">0</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel - Customization Form -->
                <div class="customization-panel">
                    <form id="customization-form" class="customize-form">
                        <!-- Step 1: Fabric Selection -->
                        <div class="form-step active" id="step-fabric" data-step="1">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-cut"></i>
                                </div>
                                <div class="step-content">
                                    <h3>Choose Your Fabric</h3>
                                    <p>Select from our premium fabric collection crafted for elegance and comfort</p>
                                </div>
                            </div>

                            <div class="fabric-grid" id="fabric-options">
                                <div class="fabric-option" data-fabric="silk">
                                    <input type="radio" name="fabric" value="silk" id="fabric-silk">
                                    <label for="fabric-silk" class="fabric-card">
                                        <div class="fabric-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="fabric-image">
                                            <img src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=150&h=150&fit=crop" alt="Silk Fabric">
                                        </div>
                                        <div class="fabric-info">
                                            <h4>Premium Silk</h4>
                                            <p>Luxurious and smooth</p>
                                            <span class="fabric-price">+₹500</span>
                                        </div>
                                    </label>
                                </div>

                                <div class="fabric-option" data-fabric="cotton">
                                    <input type="radio" name="fabric" value="cotton" id="fabric-cotton">
                                    <label for="fabric-cotton" class="fabric-card">
                                        <div class="fabric-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="fabric-image">
                                            <img src="https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=150&h=150&fit=crop" alt="Cotton Fabric">
                                        </div>
                                        <div class="fabric-info">
                                            <h4>Organic Cotton</h4>
                                            <p>Breathable and comfortable</p>
                                            <span class="fabric-price">Base price</span>
                                        </div>
                                    </label>
                                </div>

                                <div class="fabric-option" data-fabric="linen">
                                    <input type="radio" name="fabric" value="linen" id="fabric-linen">
                                    <label for="fabric-linen" class="fabric-card">
                                        <div class="fabric-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="fabric-image">
                                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=150&h=150&fit=crop" alt="Linen Fabric">
                                        </div>
                                        <div class="fabric-info">
                                            <h4>Pure Linen</h4>
                                            <p>Natural and elegant</p>
                                            <span class="fabric-price">+₹300</span>
                                        </div>
                                    </label>
                                </div>

                                <div class="fabric-option" data-fabric="chiffon">
                                    <input type="radio" name="fabric" value="chiffon" id="fabric-chiffon">
                                    <label for="fabric-chiffon" class="fabric-card">
                                        <div class="fabric-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="fabric-image">
                                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=150&h=150&fit=crop" alt="Chiffon Fabric">
                                        </div>
                                        <div class="fabric-info">
                                            <h4>Flowing Chiffon</h4>
                                            <p>Light and graceful</p>
                                            <span class="fabric-price">+₹400</span>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="fabric-details" id="fabric-details">
                                <div class="details-card">
                                    <h4>Fabric Details</h4>
                                    <p>Select a fabric to see detailed information about its properties, care instructions, and styling recommendations.</p>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-secondary" onclick="previousStep()" disabled>
                                    <i class="fas fa-arrow-left"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">
                                    Next Step
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Color Selection -->
                        <div class="form-step" id="step-color" data-step="2">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="step-content">
                                    <h3>Choose Your Color</h3>
                                    <p>Select the perfect color that matches your style and occasion</p>
                                </div>
                            </div>

                            <div class="color-grid">
                                <div class="color-option" data-color="navy">
                                    <input type="radio" name="color" value="navy" id="color-navy">
                                    <label for="color-navy" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #1e3a8a;"></div>
                                        <div class="color-info">
                                            <h4>Navy Blue</h4>
                                            <p>Classic and elegant</p>
                                        </div>
                                    </label>
                                </div>

                                <div class="color-option" data-color="burgundy">
                                    <input type="radio" name="color" value="burgundy" id="color-burgundy">
                                    <label for="color-burgundy" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #7c2d12;"></div>
                                        <div class="color-info">
                                            <h4>Burgundy</h4>
                                            <p>Rich and sophisticated</p>
                                        </div>
                                    </label>
                                </div>

                                <div class="color-option" data-color="emerald">
                                    <input type="radio" name="color" value="emerald" id="color-emerald">
                                    <label for="color-emerald" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #047857;"></div>
                                        <div class="color-info">
                                            <h4>Emerald Green</h4>
                                            <p>Vibrant and fresh</p>
                                        </div>
                                    </label>
                                </div>

                                <div class="color-option" data-color="blush">
                                    <input type="radio" name="color" value="blush" id="color-blush">
                                    <label for="color-blush" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #f3e8ff;"></div>
                                        <div class="color-info">
                                            <h4>Blush Pink</h4>
                                            <p>Soft and feminine</p>
                                        </div>
                                    </label>
                                </div>

                                <div class="color-option" data-color="charcoal">
                                    <input type="radio" name="color" value="charcoal" id="color-charcoal">
                                    <label for="color-charcoal" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #374151;"></div>
                                        <div class="color-info">
                                            <h4>Charcoal Gray</h4>
                                            <p>Professional and versatile</p>
                                        </div>
                                    </label>
                                </div>

                                <div class="color-option" data-color="cream">
                                    <input type="radio" name="color" value="cream" id="color-cream">
                                    <label for="color-cream" class="color-card">
                                        <div class="color-selector">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="color-swatch" style="background: #fef3c7;"></div>
                                        <div class="color-info">
                                            <h4>Cream</h4>
                                            <p>Timeless and elegant</p>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                    <i class="fas fa-arrow-left"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">
                                    Next Step
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Measurements -->
                        <div class="form-step" id="step-measurements" data-step="3">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-ruler"></i>
                                </div>
                                <div class="step-content">
                                    <h3>Your Measurements</h3>
                                    <p>We'll use your saved measurements for the perfect fit</p>
                                </div>
                            </div>

                            <div class="measurements-section">
                                <div class="measurements-grid">
                                    <div class="measurement-item">
                                        <label for="bust">Bust (inches)</label>
                                        <input type="number" id="bust" name="bust" min="20" max="60" step="0.5" required>
                                    </div>
                                    <div class="measurement-item">
                                        <label for="waist">Waist (inches)</label>
                                        <input type="number" id="waist" name="waist" min="20" max="50" step="0.5" required>
                                    </div>
                                    <div class="measurement-item">
                                        <label for="hips">Hips (inches)</label>
                                        <input type="number" id="hips" name="hips" min="25" max="60" step="0.5" required>
                                    </div>
                                    <div class="measurement-item">
                                        <label for="length">Length (inches)</label>
                                        <input type="number" id="length" name="length" min="30" max="60" step="0.5" required>
                                    </div>
                                    <div class="measurement-item">
                                        <label for="shoulder">Shoulder (inches)</label>
                                        <input type="number" id="shoulder" name="shoulder" min="10" max="25" step="0.5" required>
                                    </div>
                                    <div class="measurement-item">
                                        <label for="sleeve">Sleeve (inches)</label>
                                        <input type="number" id="sleeve" name="sleeve" min="15" max="35" step="0.5" required>
                                    </div>
                                </div>

                                <div class="measurements-actions">
                                    <button type="button" class="btn btn-secondary" onclick="loadSavedMeasurements()">
                                        <i class="fas fa-download"></i>
                                        Load Saved Measurements
                                    </button>
                                    <button type="button" class="btn btn-outline" onclick="saveMeasurements()">
                                        <i class="fas fa-save"></i>
                                        Save These Measurements
                                    </button>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                    <i class="fas fa-arrow-left"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">
                                    Next Step
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 4: Delivery -->
                        <div class="form-step" id="step-delivery" data-step="4">
                            <div class="step-header">
                                <div class="step-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="step-content">
                                    <h3>Delivery Details</h3>
                                    <p>Choose your delivery address and timeline</p>
                                </div>
                            </div>

                            <div class="delivery-section">
                                <div class="saved-addresses" id="saved-addresses">
                                    <h4>Saved Addresses</h4>
                                    <!-- Addresses will be populated by JavaScript -->
                                </div>

                                <div class="delivery-timeline">
                                    <h4>Delivery Timeline</h4>
                                    <div class="timeline-options">
                                        <div class="timeline-option">
                                            <input type="radio" name="delivery" value="standard" id="delivery-standard" checked>
                                            <label for="delivery-standard" class="timeline-card">
                                                <div class="timeline-info">
                                                    <h5>Standard Delivery</h5>
                                                    <p>7-10 business days</p>
                                                    <span class="timeline-price">Free</span>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="timeline-option">
                                            <input type="radio" name="delivery" value="express" id="delivery-express">
                                            <label for="delivery-express" class="timeline-card">
                                                <div class="timeline-info">
                                                    <h5>Express Delivery</h5>
                                                    <p>3-5 business days</p>
                                                    <span class="timeline-price">+₹200</span>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="timeline-option">
                                            <input type="radio" name="delivery" value="rush" id="delivery-rush">
                                            <label for="delivery-rush" class="timeline-card">
                                                <div class="timeline-info">
                                                    <h5>Rush Order</h5>
                                                    <p>1-2 business days</p>
                                                    <span class="timeline-price">+₹500</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="step-actions">
                                <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                    <i class="fas fa-arrow-left"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" onclick="nextStep()">
                                    Complete Order
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Address Modal -->
    <div id="address-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddressModal()">&times;</span>
            <h2>Add New Address</h2>
            <form id="address-form">
                <div class="form-group">
                    <label for="address-name">Address Name (e.g., Home, Office)</label>
                    <input type="text" id="address-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="address-line1">Address Line 1</label>
                    <input type="text" id="address-line1" name="line1" required>
                </div>
                <div class="form-group">
                    <label for="address-line2">Address Line 2</label>
                    <input type="text" id="address-line2" name="line2">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-city">City</label>
                        <input type="text" id="address-city" name="city" required>
                    </div>
                    <div class="form-group">
                        <label for="address-state">State</label>
                        <input type="text" id="address-state" name="state" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="address-pincode">Pincode</label>
                        <input type="text" id="address-pincode" name="pincode" pattern="[0-9]{6}" required>
                    </div>
                    <div class="form-group">
                        <label for="address-phone">Phone</label>
                        <input type="tel" id="address-phone" name="phone" pattern="[0-9]{10}" required>
                    </div>
                </div>
                <button type="submit" class="cta-button">Save Address</button>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="auth.js"></script>
    <script>
        let selectedDress = null;
        let userData = null;
        let savedAddresses = [];
        let savedMeasurements = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Customize page - DOMContentLoaded');
            loadUserData();
            loadSelectedDress();
            loadSavedData();
            setupEventListeners();
            console.log('Customize page - Initialization complete');
        });

        // Load user data
        function loadUserData() {
            // Check both localStorage and sessionStorage
            const localUserData = localStorage.getItem('userData');
            const sessionUserData = sessionStorage.getItem('userData');
            const userDataString = localUserData || sessionUserData;

            if (userDataString) {
                try {
                    userData = JSON.parse(userDataString);
                } catch (e) {
                    console.error('Error parsing user data:', e);
                }
            }

            // Also check for token to determine if user is authenticated
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');

            // Only redirect to login if no userData and no valid token
            if (!userData && !token) {
                console.log('No user data or token found, redirecting to login');
                window.location.href = 'login.html';
                return;
            }

            console.log('User data loaded:', userData ? userData.name || userData.email : 'No user data');
        }

        // Load selected dress
        function loadSelectedDress() {
            selectedDress = JSON.parse(localStorage.getItem('selectedDress'));
            if (!selectedDress) {
                console.log('No selected dress found, redirecting to homepage');
                window.location.href = 'index.html';
                return;
            }

            console.log('Loading selected dress:', selectedDress.name);

            // Update dress image
            const dressImage = document.getElementById('dress-image');
            if (dressImage) {
                dressImage.src = selectedDress.image;
                dressImage.alt = selectedDress.name;
            }

            // Update dress name
            const dressName = document.getElementById('dress-name');
            if (dressName) {
                dressName.textContent = selectedDress.name;
            }

            // Update dress description
            const dressDesc = document.getElementById('dress-description');
            if (dressDesc) {
                dressDesc.textContent = selectedDress.description;
            }

            // Update pricing
            const basePrice = document.getElementById('base-price');
            const totalPrice = document.getElementById('total-price');
            if (basePrice) {
                basePrice.textContent = selectedDress.price;
            }
            if (totalPrice) {
                totalPrice.textContent = selectedDress.price;
            }

            console.log('Dress loaded successfully');
            populateOptions();
        }

        // Populate fabric, color, and size options
        function populateOptions() {
            console.log('Populating options for dress:', selectedDress.name);

            // The fabric options are now static in HTML, so we don't need to populate them
            // Just ensure the first option is selected by default
            const firstFabricOption = document.querySelector('input[name="fabric"]');
            if (firstFabricOption) {
                firstFabricOption.checked = true;
                updateSelection('fabric', firstFabricOption.value);
            }

            // For now, we'll use the static options in the HTML
            // In a full implementation, you could dynamically populate based on selectedDress data
            console.log('Options populated successfully');
        }

        // Update selection display
        function updateSelection(type, value) {
            const selectionElement = document.getElementById(`selected-${type}`);
            if (selectionElement) {
                selectionElement.textContent = value;
            }
            updatePrice();
        }

        // Update price calculation
        function updatePrice() {
            if (!selectedDress) return;

            let basePrice = parseInt(selectedDress.price) || 3000;
            let customPrice = 0;
            let deliveryPrice = 0;

            // Add fabric cost
            const selectedFabric = document.querySelector('input[name="fabric"]:checked');
            if (selectedFabric) {
                switch(selectedFabric.value) {
                    case 'silk':
                        customPrice += 500;
                        break;
                    case 'linen':
                        customPrice += 300;
                        break;
                    case 'chiffon':
                        customPrice += 400;
                        break;
                    default:
                        customPrice += 0;
                }
            }

            // Add delivery cost
            const selectedDelivery = document.querySelector('input[name="delivery"]:checked');
            if (selectedDelivery) {
                switch(selectedDelivery.value) {
                    case 'express':
                        deliveryPrice = 200;
                        break;
                    case 'rush':
                        deliveryPrice = 500;
                        break;
                    default:
                        deliveryPrice = 0;
                }
            }

            const totalPrice = basePrice + customPrice + deliveryPrice;

            // Update price display in preview panel
            const basePriceElement = document.getElementById('base-price');
            const customPriceElement = document.getElementById('custom-price');
            const totalPriceElement = document.getElementById('total-price');

            if (basePriceElement) basePriceElement.textContent = basePrice;
            if (customPriceElement) customPriceElement.textContent = customPrice;
            if (totalPriceElement) totalPriceElement.textContent = totalPrice;

            console.log('Price updated:', { basePrice, customPrice, deliveryPrice, totalPrice });
        }

        // Step navigation
        let currentStep = 1;
        const totalSteps = 4;

        function nextStep() {
            console.log('Next step clicked, current step:', currentStep);

            // Validate current step before proceeding
            if (!validateCurrentStep()) {
                return;
            }

            if (currentStep < totalSteps) {
                // Hide current step
                const currentStepElement = document.querySelector('.form-step.active');
                if (currentStepElement) {
                    currentStepElement.classList.remove('active');
                }

                // Show next step
                currentStep++;
                const nextStepElement = document.getElementById(getStepId(currentStep));
                if (nextStepElement) {
                    nextStepElement.classList.add('active');
                }

                // Update progress indicator
                updateProgressIndicator();

                console.log('Moved to step:', currentStep);
            } else {
                // Final step - place order
                placeOrder();
            }
        }

        function previousStep() {
            console.log('Previous step clicked, current step:', currentStep);

            if (currentStep > 1) {
                // Hide current step
                const currentStepElement = document.querySelector('.form-step.active');
                if (currentStepElement) {
                    currentStepElement.classList.remove('active');
                }

                // Show previous step
                currentStep--;
                const prevStepElement = document.getElementById(getStepId(currentStep));
                if (prevStepElement) {
                    prevStepElement.classList.add('active');
                }

                // Update progress indicator
                updateProgressIndicator();

                console.log('Moved to step:', currentStep);
            }
        }

        function getStepId(stepNumber) {
            const stepIds = {
                1: 'step-fabric',
                2: 'step-color',
                3: 'step-measurements',
                4: 'step-delivery'
            };
            return stepIds[stepNumber];
        }

        function validateCurrentStep() {
            switch(currentStep) {
                case 1: // Fabric
                    const selectedFabric = document.querySelector('input[name="fabric"]:checked');
                    if (!selectedFabric) {
                        showMessage('Please select a fabric before continuing', 'error');
                        return false;
                    }
                    break;
                case 2: // Color
                    const selectedColor = document.querySelector('input[name="color"]:checked');
                    if (!selectedColor) {
                        showMessage('Please select a color before continuing', 'error');
                        return false;
                    }
                    break;
                case 3: // Measurements
                    // Validate measurements if needed
                    break;
                case 4: // Delivery
                    // Validate delivery details if needed
                    break;
            }
            return true;
        }

        function updateProgressIndicator() {
            // Update progress steps
            const progressSteps = document.querySelectorAll('.progress-step');
            progressSteps.forEach((step, index) => {
                const stepNumber = index + 1;
                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    step.classList.remove('active', 'completed');
                }
            });

            // Update progress bar
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                const progressPercentage = (currentStep / totalSteps) * 100;
                progressFill.style.width = `${progressPercentage}%`;
            }
        }

        // Zoom image function
        function zoomImage() {
            console.log('Zoom image clicked');
            // For now, just show a message
            showMessage('Image zoom functionality will be implemented', 'info');
        }

        // Place order function
        function placeOrder() {
            console.log('Placing order...');

            // Generate unique order ID
            const orderId = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();

            // Get selected address
            const selectedAddressId = document.querySelector('input[name="address"]:checked')?.value;
            const selectedAddress = savedAddresses.find(addr => addr.id == selectedAddressId) || savedAddresses[0];

            // Calculate delivery date based on delivery option
            const deliveryOption = document.querySelector('input[name="delivery"]:checked')?.value || 'standard';
            let deliveryDays = 7; // default
            switch(deliveryOption) {
                case 'rush':
                    deliveryDays = 2;
                    break;
                case 'express':
                    deliveryDays = 4;
                    break;
                case 'standard':
                default:
                    deliveryDays = 7;
                    break;
            }

            const estimatedDelivery = new Date();
            estimatedDelivery.setDate(estimatedDelivery.getDate() + deliveryDays);

            // Collect complete order data
            const orderData = {
                id: orderId,
                orderNumber: orderId,
                status: 'confirmed',
                orderDate: new Date().toISOString(),
                estimatedDelivery: estimatedDelivery.toISOString(),
                customer: {
                    name: userData?.name || 'Demo User',
                    email: userData?.email || '<EMAIL>',
                    phone: selectedAddress?.phone || '+91 98765 43210'
                },
                dress: {
                    id: selectedDress?.id || 1,
                    name: selectedDress?.name || 'Custom Dress',
                    image: selectedDress?.image || '',
                    basePrice: parseInt(selectedDress?.price) || 3000
                },
                customization: {
                    fabric: document.querySelector('input[name="fabric"]:checked')?.value || 'cotton',
                    color: document.querySelector('input[name="color"]:checked')?.value || 'navy',
                    measurements: {
                        bust: parseFloat(document.getElementById('bust')?.value) || 0,
                        waist: parseFloat(document.getElementById('waist')?.value) || 0,
                        hips: parseFloat(document.getElementById('hips')?.value) || 0,
                        length: parseFloat(document.getElementById('length')?.value) || 0,
                        shoulder: parseFloat(document.getElementById('shoulder')?.value) || 0,
                        sleeve: parseFloat(document.getElementById('sleeve')?.value) || 0
                    }
                },
                delivery: {
                    option: deliveryOption,
                    address: selectedAddress,
                    estimatedDate: estimatedDelivery.toLocaleDateString('en-IN', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    })
                },
                pricing: {
                    basePrice: parseInt(selectedDress?.price) || 3000,
                    fabricUpgrade: getFabricPrice(document.querySelector('input[name="fabric"]:checked')?.value),
                    deliveryCharge: getDeliveryPrice(deliveryOption),
                    totalPrice: parseInt(document.getElementById('total-price')?.textContent) || 3000
                },
                trackingNumber: 'TRK-' + Math.random().toString(36).substr(2, 8).toUpperCase()
            };

            console.log('Complete order data:', orderData);

            // Store order data for order success page
            localStorage.setItem('lastOrder', JSON.stringify(orderData));
            localStorage.setItem('orderSuccess', JSON.stringify(orderData));

            // Show success message
            showMessage(`Order ${orderId} placed successfully! Redirecting to confirmation...`, 'success');

            // Redirect to order success page after a short delay
            setTimeout(() => {
                window.location.href = 'order-success.html';
            }, 2000);
        }

        // Helper function to get fabric price
        function getFabricPrice(fabric) {
            switch(fabric) {
                case 'silk': return 500;
                case 'linen': return 300;
                case 'chiffon': return 400;
                default: return 0;
            }
        }

        // Helper function to get delivery price
        function getDeliveryPrice(delivery) {
            switch(delivery) {
                case 'rush': return 500;
                case 'express': return 200;
                default: return 0;
            }
        }

        // Load saved measurements function (enhanced)
        function loadSavedMeasurements() {
            if (savedMeasurements) {
                document.getElementById('bust').value = savedMeasurements.bust || '';
                document.getElementById('waist').value = savedMeasurements.waist || '';
                document.getElementById('hips').value = savedMeasurements.hips || '';
                document.getElementById('length').value = savedMeasurements.length || '';
                document.getElementById('shoulder').value = savedMeasurements.shoulder || '';
                document.getElementById('sleeve').value = savedMeasurements.sleeve || '';
                showMessage('Saved measurements loaded successfully!', 'success');
            } else {
                showMessage('No saved measurements found', 'info');
            }
        }

        // Add event listeners for all selections
        function setupEventListeners() {
            console.log('Setting up event listeners');

            // Fabric selection listeners
            const fabricOptions = document.querySelectorAll('input[name="fabric"]');
            console.log('Found fabric options:', fabricOptions.length);

            fabricOptions.forEach((option, index) => {
                console.log(`Setting up listener for fabric option ${index}:`, option.value);

                option.addEventListener('change', function() {
                    console.log('Fabric changed to:', this.value);
                    if (this.checked) {
                        updateSelection('fabric', this.value);
                        updateFabricDetails(this.value);
                    }
                });
            });

            // Color selection listeners
            const colorOptions = document.querySelectorAll('input[name="color"]');
            console.log('Found color options:', colorOptions.length);

            colorOptions.forEach((option, index) => {
                console.log(`Setting up listener for color option ${index}:`, option.value);

                option.addEventListener('change', function() {
                    console.log('Color changed to:', this.value);
                    if (this.checked) {
                        updateSelection('color', this.value);
                    }
                });
            });

            // Delivery option listeners
            const deliveryOptions = document.querySelectorAll('input[name="delivery"]');
            console.log('Found delivery options:', deliveryOptions.length);

            deliveryOptions.forEach((option, index) => {
                option.addEventListener('change', function() {
                    console.log('Delivery changed to:', this.value);
                    if (this.checked) {
                        updatePrice();
                    }
                });
            });

            console.log('Event listeners setup complete');
        }

        // Update fabric details
        function updateFabricDetails(fabric) {
            const detailsCard = document.querySelector('.details-card');
            if (!detailsCard) return;

            const fabricDetails = {
                silk: {
                    title: 'Premium Silk',
                    description: 'Luxurious silk fabric with natural sheen and smooth texture. Perfect for formal occasions. Dry clean only.',
                    properties: ['Luxurious feel', 'Natural sheen', 'Breathable', 'Elegant drape']
                },
                cotton: {
                    title: 'Organic Cotton',
                    description: 'High-quality organic cotton that\'s comfortable and breathable. Perfect for everyday wear. Machine washable.',
                    properties: ['Comfortable', 'Breathable', 'Easy care', 'Durable']
                },
                linen: {
                    title: 'Pure Linen',
                    description: 'Natural linen fabric with a relaxed, elegant look. Perfect for casual and semi-formal occasions.',
                    properties: ['Natural texture', 'Lightweight', 'Breathable', 'Casual elegance']
                },
                chiffon: {
                    title: 'Flowing Chiffon',
                    description: 'Light and airy chiffon fabric that creates beautiful flowing silhouettes. Perfect for special occasions.',
                    properties: ['Lightweight', 'Flowing', 'Elegant movement', 'Special occasions']
                }
            };

            const details = fabricDetails[fabric];
            if (details) {
                detailsCard.innerHTML = `
                    <h4>${details.title}</h4>
                    <p>${details.description}</p>
                    <div class="fabric-properties">
                        ${details.properties.map(prop => `<span class="property-tag">${prop}</span>`).join('')}
                    </div>
                `;
            }
        }

        // Display saved addresses in delivery step
        function displaySavedAddresses() {
            const container = document.getElementById('saved-addresses');
            if (!container) return;

            if (savedAddresses && savedAddresses.length > 0) {
                container.innerHTML = savedAddresses.map((address, index) => `
                    <label class="address-card ${address.isDefault ? 'selected' : ''}" data-address-id="${address.id}">
                        <input type="radio" name="address" value="${address.id}" ${address.isDefault ? 'checked' : ''}>
                        <div class="address-content">
                            <h5>${address.name}</h5>
                            <p>${address.fullName}</p>
                            <p>${address.address}</p>
                            <p>${address.city}, ${address.state} ${address.zipCode}</p>
                            <p>${address.phone}</p>
                        </div>
                    </label>
                `).join('');

                // Add event listeners to address cards
                const addressCards = container.querySelectorAll('.address-card');
                addressCards.forEach(card => {
                    card.addEventListener('click', function() {
                        // Remove selected class from all cards
                        addressCards.forEach(c => c.classList.remove('selected'));
                        // Add selected class to clicked card
                        this.classList.add('selected');
                        // Check the radio button
                        const radio = this.querySelector('input[type="radio"]');
                        if (radio) radio.checked = true;
                    });
                });
            } else {
                container.innerHTML = '<p>No saved addresses found. Please add a new address.</p>';
            }
        }

        // Get color hex values
        function getColorHex(color) {
            const colorMap = {
                'Red': '#e74c3c',
                'Blue': '#3498db',
                'Green': '#27ae60',
                'Yellow': '#f1c40f',
                'Black': '#2c3e50',
                'White': '#ecf0f1',
                'Pink': '#ff69b4',
                'Purple': '#9b59b6',
                'Orange': '#e67e22',
                'Maroon': '#8b0000',
                'Gold': '#ffd700',
                'Silver': '#c0c0c0',
                'Navy': '#000080',
                'Teal': '#008080',
                'Grey': '#7f8c8d'
            };
            return colorMap[color] || '#95a5a6';
        }

        // Load saved data
        async function loadSavedData() {
            try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');



                // Only make API call for non-demo tokens
                if (!token) {
                    // Redirect to login if no token
                    window.location.href = 'login.html';
                    return;
                }

                const response = await fetch('/api/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const profile = await response.json();
                    savedAddresses = profile.addresses || [];
                    savedMeasurements = profile.measurements || null;

                    displaySavedAddresses();

                    if (savedMeasurements) {
                        loadSavedMeasurements();
                    }
                } else if (response.status === 401) {
                    // Only redirect to login for non-demo tokens
                    window.location.href = 'login.html';
                }
            } catch (error) {
                console.error('Error loading saved data:', error);
                window.location.href = 'login.html';
            }
        }

        // Display saved addresses
        function displaySavedAddresses() {
            const container = document.getElementById('saved-addresses');
            if (savedAddresses.length > 0) {
                container.innerHTML = savedAddresses.map((address, index) => `
                    <label class="address-card">
                        <input type="radio" name="address" value="${index}" ${index === 0 ? 'checked' : ''}>
                        <div>
                            <strong>${address.name}</strong><br>
                            ${address.line1}, ${address.line2 || ''}<br>
                            ${address.city}, ${address.state} - ${address.pincode}<br>
                            Phone: ${address.phone}
                        </div>
                    </label>
                `).join('');
            } else {
                container.innerHTML = '<p>No saved addresses. Please add a new address.</p>';
            }
        }

        // Load saved measurements
        function loadSavedMeasurements() {
            if (savedMeasurements) {
                document.getElementById('bust').value = savedMeasurements.bust || '';
                document.getElementById('waist').value = savedMeasurements.waist || '';
                document.getElementById('hips').value = savedMeasurements.hips || '';
                document.getElementById('length').value = savedMeasurements.length || '';
                document.getElementById('shoulder').value = savedMeasurements.shoulder || '';
                document.getElementById('sleeve').value = savedMeasurements.sleeve || '';
            }
        }

        // Save measurements
        async function saveMeasurements() {
            const measurements = {
                bust: parseFloat(document.getElementById('bust').value),
                waist: parseFloat(document.getElementById('waist').value),
                hips: parseFloat(document.getElementById('hips').value),
                length: parseFloat(document.getElementById('length').value),
                shoulder: parseFloat(document.getElementById('shoulder').value),
                sleeve: parseFloat(document.getElementById('sleeve').value)
            };

            try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                const response = await fetch('/api/auth/measurements', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(measurements)
                });

                if (response.ok) {
                    showMessage('Measurements saved successfully!', 'success');
                    savedMeasurements = measurements;
                } else {
                    showMessage('Failed to save measurements', 'error');
                }
            } catch (error) {
                console.error('Error saving measurements:', error);
                showMessage('Error saving measurements', 'error');
            }
        }

        // Add new address
        function addNewAddress() {
            document.getElementById('address-modal').style.display = 'block';
        }

        // Close address modal
        function closeAddressModal() {
            document.getElementById('address-modal').style.display = 'none';
        }

        // Address form submission
        document.getElementById('address-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const address = {
                name: document.getElementById('address-name').value,
                line1: document.getElementById('address-line1').value,
                line2: document.getElementById('address-line2').value,
                city: document.getElementById('address-city').value,
                state: document.getElementById('address-state').value,
                pincode: document.getElementById('address-pincode').value,
                phone: document.getElementById('address-phone').value
            };

            try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                const response = await fetch('/api/auth/addresses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(address)
                });

                if (response.ok) {
                    const newAddress = await response.json();
                    savedAddresses.push(newAddress);
                    displaySavedAddresses();
                    closeAddressModal();
                    document.getElementById('address-form').reset();
                    showMessage('Address added successfully!', 'success');
                } else {
                    showMessage('Failed to add address', 'error');
                }
            } catch (error) {
                console.error('Error adding address:', error);
                showMessage('Error adding address', 'error');
            }
        });









        // Show message
        function showMessage(text, type = 'info') {
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${text}
            `;
            
            message.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                message.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }, 3000);
        }

        // Mobile menu functions
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobile-nav');
            const mobileOverlay = document.getElementById('mobile-overlay');
            
            mobileNav.classList.toggle('active');
            mobileOverlay.classList.toggle('active');
            document.body.style.overflow = mobileNav.classList.contains('active') ? 'hidden' : 'auto';
        }

        function closeMobileMenu() {
            const mobileNav = document.getElementById('mobile-nav');
            const mobileOverlay = document.getElementById('mobile-overlay');
            
            mobileNav.classList.remove('active');
            mobileOverlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }
    </script>

    <style>
        .customization-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .customization-preview {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .dress-preview {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .dress-preview img {
            width: 300px;
            height: 400px;
            object-fit: cover;
            border-radius: 10px;
        }

        .dress-info h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .price-display {
            font-size: 1.5rem;
            margin-top: 1rem;
        }

        .price-value {
            color: #ff6b9d;
            font-weight: bold;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #ff6b9d;
            padding-bottom: 0.5rem;
        }

        .fabric-options, .color-options, .size-options {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .option-card {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            border: 2px solid #ecf0f1;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option-card:hover {
            border-color: #ff6b9d;
        }

        .option-card input[type="radio"] {
            display: none;
        }

        .option-card input[type="radio"]:checked + span {
            color: #ff6b9d;
            font-weight: bold;
        }

        .option-card:has(input[type="radio"]:checked) {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .color-option span:first-child {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ecf0f1;
        }

        .measurements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .measurement-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .measurement-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
        }

        .measurement-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .delivery-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .delivery-option {
            display: block;
            cursor: pointer;
        }

        .delivery-option input[type="radio"] {
            display: none;
        }

        .delivery-card {
            padding: 1.5rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .delivery-option input[type="radio"]:checked + .delivery-card {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .delivery-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .delivery-price {
            color: #ff6b9d;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .address-card {
            display: block;
            padding: 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .address-card:has(input[type="radio"]:checked) {
            border-color: #ff6b9d;
            background: rgba(255, 107, 157, 0.1);
        }

        .order-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .summary-row.total {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ff6b9d;
            border-top: 2px solid #ecf0f1;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }

        textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            resize: vertical;
        }

        @media (max-width: 768px) {
            .dress-preview {
                flex-direction: column;
                text-align: center;
            }

            .dress-preview img {
                width: 100%;
                max-width: 300px;
            }

            .measurements-grid {
                grid-template-columns: 1fr;
            }

            .delivery-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>