/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette */
    --primary-50: #fef7f0;
    --primary-100: #fdeee0;
    --primary-200: #fad9c1;
    --primary-300: #f6be97;
    --primary-400: #f19a6b;
    --primary-500: #ed7d47;
    --primary-600: #de6332;
    --primary-700: #b84d28;
    --primary-800: #933f26;
    --primary-900: #773622;

    /* Accent Colors */
    --accent-50: #fdf4f8;
    --accent-100: #fce8f1;
    --accent-200: #f9d2e4;
    --accent-300: #f4aecf;
    --accent-400: #ec7bb0;
    --accent-500: #e24d93;
    --accent-600: #d02d75;
    --accent-700: #b01e5e;
    --accent-800: #921b4e;
    --accent-900: #7a1a43;

    /* Neutral Colors */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;

    /* Semantic Colors */
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;
    --info-500: #3b82f6;

    /* Typography */
    --font-family-sans: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-serif: 'Playfair Display', Georgia, serif;

    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    color: var(--neutral-800);
    overflow-x: hidden;
    font-size: var(--text-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-5);
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    padding: var(--space-4) 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-family: var(--font-family-serif);
    font-size: var(--text-2xl);
    margin-bottom: var(--space-1);
    font-weight: 700;
    letter-spacing: -0.025em;
}

.nav-brand p {
    font-size: var(--text-sm);
    opacity: 0.9;
    font-weight: 400;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-100);
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--primary-100);
    border-radius: var(--radius-full);
}

.btn-login {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: var(--space-2) var(--space-5);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: var(--text-sm);
    backdrop-filter: blur(10px);
    min-height: 44px; /* Accessibility: minimum touch target */
}

.btn-login:hover {
    background: white;
    color: var(--primary-600);
    border-color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.mobile-menu-toggle {
    display: none;
    font-size: var(--text-xl);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    min-height: 44px; /* Accessibility: minimum touch target */
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-50), var(--accent-50), var(--neutral-50));
    padding: calc(var(--space-24) + var(--space-8)) 0 var(--space-20);
    display: flex;
    align-items: center;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, var(--primary-100) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, var(--accent-100) 0%, transparent 50%);
    opacity: 0.6;
    z-index: 1;
}

.hero-content {
    flex: 1;
    padding: 0 var(--space-5);
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-family: var(--font-family-serif);
    font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    line-height: 1.1;
    letter-spacing: -0.025em;
}

.hero-content p {
    font-size: var(--text-xl);
    color: var(--neutral-600);
    margin-bottom: var(--space-8);
    max-width: 600px;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
    flex-wrap: wrap;
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    background: rgba(255, 255, 255, 0.9);
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-fast);
}

.feature:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.feature i {
    color: var(--primary-600);
    font-size: var(--text-lg);
}

.cta-button {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    border: none;
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    min-height: 48px; /* Accessibility: minimum touch target */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.cta-button:active {
    transform: translateY(-1px);
}

.hero-image {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    transition: var(--transition-normal);
}

.hero-image img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-2xl);
}

/* Enhanced Hero Styles */
.hero-title {
    overflow: hidden;
}

.hero-title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(50px);
}

.hero-title-line:nth-child(2) {
    animation-delay: 0.2s;
}

.hero-description {
    animation: fadeInUp 1s ease-out 0.4s forwards;
    opacity: 0;
}

.hero-features {
    animation: fadeInUp 1s ease-out 0.6s forwards;
    opacity: 0;
}

.hero-cta {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.8s forwards;
    opacity: 0;
}

.cta-button.secondary {
    background: transparent;
    color: var(--primary-600);
    border: 2px solid var(--primary-500);
}

.cta-button.secondary:hover {
    background: var(--primary-500);
    color: white;
    border-color: var(--primary-500);
}

.hero-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--primary-500) 0%, transparent 70%);
    opacity: 0.1;
    border-radius: var(--radius-2xl);
}

.scroll-indicator {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    box-shadow: var(--shadow-md);
    transition: var(--transition-fast);
}

.scroll-arrow:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Dresses Section */
.dresses-section {
    padding: var(--space-20) 0;
    background: var(--neutral-50);
}

.dresses-section h2 {
    font-family: var(--font-family-serif);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    text-align: center;
    margin-bottom: var(--space-12);
    color: var(--neutral-900);
    font-weight: 700;
    letter-spacing: -0.025em;
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin-bottom: var(--space-12);
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    border: 2px solid var(--primary-500);
    color: var(--primary-600);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: var(--text-sm);
    min-height: 44px; /* Accessibility: minimum touch target */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.filter-btn:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Enhanced Section Styles */
.section-header {
    text-align: center;
    margin-bottom: var(--space-12);
}

.section-title {
    font-family: var(--font-family-serif);
    font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
    color: var(--neutral-900);
    font-weight: 700;
    letter-spacing: -0.025em;
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.collection-stats {
    text-align: center;
    margin-bottom: var(--space-8);
    color: var(--neutral-600);
    font-size: var(--text-sm);
}

/* Loading Skeleton */
.loading-skeleton {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
}

.skeleton-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-card::before {
    content: '';
    display: block;
    height: 280px;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.skeleton-card::after {
    content: '';
    display: block;
    height: 120px;
    background: var(--neutral-100);
    padding: var(--space-6);
}

/* About Section Enhancements */
.about-header {
    margin-bottom: var(--space-8);
}

.about-intro {
    font-size: var(--text-lg);
    color: var(--neutral-600);
    line-height: 1.7;
    margin-top: var(--space-4);
}

.feature-item {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
    margin-bottom: var(--space-8);
    padding: var(--space-6);
    background: var(--neutral-50);
    border-radius: var(--radius-xl);
    border: 1px solid var(--neutral-200);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-200);
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-xl);
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
}

.feature-content p {
    color: var(--neutral-600);
    line-height: 1.6;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
    padding: var(--space-8);
    background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
    border-radius: var(--radius-xl);
    border: 1px solid var(--primary-200);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--text-4xl);
    font-weight: 700;
    color: var(--primary-600);
    font-family: var(--font-family-serif);
}

.stat-label {
    display: block;
    font-size: var(--text-sm);
    color: var(--neutral-600);
    margin-top: var(--space-1);
    font-weight: 500;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    border-radius: var(--radius-xl);
    opacity: 0;
    transition: var(--transition-normal);
    display: flex;
    align-items: flex-end;
    padding: var(--space-6);
}

.about-image:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    color: white;
}

.overlay-content h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    margin-bottom: var(--space-1);
}

.overlay-content p {
    font-size: var(--text-sm);
    opacity: 0.9;
}

.dresses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
}

.dress-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--neutral-200);
    position: relative;
}

.dress-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.dress-card:focus-within {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

.dress-image {
    height: 280px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.dress-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: var(--transition-fast);
}

.dress-card:hover .dress-image::after {
    opacity: 1;
}

.urgency-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--error-500);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.urgency-badge.medium {
    background: var(--warning-500);
}

.urgency-badge.low {
    background: var(--success-500);
}

.dress-info {
    padding: var(--space-6);
}

.dress-info h3 {
    font-family: var(--font-family-serif);
    font-size: var(--text-xl);
    margin-bottom: var(--space-2);
    color: var(--neutral-900);
    font-weight: 600;
    line-height: 1.3;
}

.dress-price {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--primary-600);
    margin-bottom: var(--space-4);
}

.dress-features {
    display: flex;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    flex-wrap: wrap;
}

.feature-tag {
    background: var(--neutral-100);
    color: var(--neutral-600);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
    border: 1px solid var(--neutral-200);
}

.customize-btn,
.order-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    border: none;
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 600;
    font-size: var(--text-base);
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    min-height: 48px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.customize-btn::before,
.order-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.customize-btn:hover::before,
.order-btn:hover::before {
    left: 100%;
}

.customize-btn:hover,
.order-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-600), var(--accent-700));
}

.customize-btn:active,
.order-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.customize-btn:disabled,
.order-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.customize-btn i,
.order-btn i {
    font-size: var(--text-lg);
}

/* Enhanced General Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--text-base);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    min-height: 44px;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--accent-700));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: white;
    color: var(--primary-600);
    border: 2px solid var(--primary-500);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--primary-50);
    border-color: var(--primary-600);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-link {
    background: transparent;
    color: var(--primary-600);
    border: none;
    box-shadow: none;
    text-decoration: underline;
    text-underline-offset: 4px;
}

.btn-link:hover {
    color: var(--primary-700);
    text-decoration-thickness: 2px;
    transform: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn i {
    font-size: var(--text-lg);
}

/* Loading Spinner for Buttons */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Button Loading States */
.btn:disabled,
.order-btn:disabled,
.customize-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:disabled i,
.order-btn:disabled i,
.customize-btn:disabled i {
    animation: spin 1s linear infinite;
}



/* Message Toast Styles */
.message-toast {
    position: fixed;
    top: var(--space-20);
    right: var(--space-6);
    z-index: 10000;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-500);
    padding: var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 500px;
    animation: slideInRight 0.3s ease-out;
}

.message-toast.message-success {
    border-left-color: var(--success-500);
}

.message-toast.message-error {
    border-left-color: var(--error-500);
}

.message-toast.message-warning {
    border-left-color: var(--warning-500);
}

.message-toast.message-info {
    border-left-color: var(--info-500);
}

.message-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;
}

.message-content i {
    font-size: var(--text-lg);
    color: var(--primary-600);
}

.message-toast.message-success .message-content i {
    color: var(--success-500);
}

.message-toast.message-error .message-content i {
    color: var(--error-500);
}

.message-toast.message-warning .message-content i {
    color: var(--warning-500);
}

.message-toast.message-info .message-content i {
    color: var(--info-500);
}

.message-close {
    background: none;
    border: none;
    color: var(--neutral-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    margin-left: var(--space-3);
}

.message-close:hover {
    color: var(--neutral-600);
    background: var(--neutral-100);
}

/* Loading Spinner for Order Process */
.loading {
    display: none;
    align-items: center;
    gap: var(--space-2);
    color: white;
}

.loading .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Progress Bar Enhancements */
.progress-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--accent-600));
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
}

/* Step Indicator Enhancements */
.progress-step.completed .step-circle {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
}

.progress-step.completed .step-number {
    display: none;
}

.progress-step.completed .step-check {
    display: block;
}

.step-check {
    display: none;
    font-size: var(--text-sm);
}

/* Form Validation States */
.form-group.error input,
.form-group.error select {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group.success input,
.form-group.success select {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.error-message {
    color: var(--error-500);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: none;
}

.form-group.error .error-message {
    display: block;
}

/* Order Success Page Styles */
.success-main {
    min-height: 100vh;
    padding: calc(var(--space-20) + var(--space-4)) 0 var(--space-16);
    background: linear-gradient(135deg, var(--primary-50), var(--accent-50), var(--neutral-50));
}

.success-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
}

.success-icon {
    text-align: center;
    padding: var(--space-12) var(--space-8) var(--space-8);
    background: linear-gradient(135deg, var(--success-500), var(--primary-500));
    color: white;
}

.success-icon i {
    font-size: 4rem;
    margin-bottom: var(--space-4);
}

.success-content {
    text-align: center;
    padding: 0 var(--space-8) var(--space-8);
}

.success-content h1 {
    font-family: var(--font-family-serif);
    font-size: var(--text-4xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    font-weight: 700;
}

.success-subtitle {
    font-size: var(--text-lg);
    color: var(--neutral-600);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.order-details {
    padding: var(--space-8);
    border-top: 1px solid var(--neutral-200);
}

.order-summary h3 {
    font-size: var(--text-2xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-6);
    font-weight: 600;
}

.order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.order-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    background: var(--neutral-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--neutral-200);
}

.order-info-item .label {
    font-weight: 500;
    color: var(--neutral-600);
}

.order-info-item .value {
    font-weight: 600;
    color: var(--neutral-900);
}

.total-amount {
    color: var(--primary-600) !important;
    font-size: var(--text-lg) !important;
}

.dress-details,
.delivery-details {
    margin-top: var(--space-8);
}

.dress-details h4,
.delivery-details h4 {
    font-size: var(--text-xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    font-weight: 600;
}

.dress-specs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
}

.spec-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    padding: var(--space-3);
    background: var(--primary-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--primary-200);
}

.spec-label {
    font-size: var(--text-sm);
    color: var(--neutral-600);
    font-weight: 500;
}

.spec-value {
    font-weight: 600;
    color: var(--neutral-900);
}

.address-card {
    background: var(--neutral-50);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    border: 1px solid var(--neutral-200);
}

.address-card p {
    margin-bottom: var(--space-2);
    color: var(--neutral-700);
}

.address-card p:last-child {
    margin-bottom: 0;
}

.address-card i {
    color: var(--primary-600);
    margin-right: var(--space-2);
}

.next-steps {
    padding: var(--space-8);
    background: var(--neutral-50);
    border-top: 1px solid var(--neutral-200);
}

.next-steps h3 {
    font-size: var(--text-2xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-6);
    text-align: center;
    font-weight: 600;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

.step-item {
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: var(--text-lg);
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
    font-weight: 600;
}

.step-content p {
    color: var(--neutral-600);
    line-height: 1.5;
}

.action-buttons {
    padding: var(--space-8);
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
    border-top: 1px solid var(--neutral-200);
}

.support-section {
    padding: var(--space-8);
    text-align: center;
    background: var(--primary-50);
    border-top: 1px solid var(--primary-200);
}

.support-section h4 {
    font-size: var(--text-xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-3);
    font-weight: 600;
}

.support-section p {
    color: var(--neutral-600);
    margin-bottom: var(--space-6);
}

.contact-options {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    flex-wrap: wrap;
}

.contact-option {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--primary-600);
    text-decoration: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    border: 1px solid var(--primary-300);
    background: white;
    transition: var(--transition-fast);
}

.contact-option:hover {
    background: var(--primary-500);
    color: white;
    border-color: var(--primary-500);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Order Status Styles */
.order-status {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: var(--warning-100);
    color: var(--warning-700);
    border: 1px solid var(--warning-300);
}

.status-in-progress {
    background: var(--info-100);
    color: var(--info-700);
    border: 1px solid var(--info-300);
}

.status-completed {
    background: var(--success-100);
    color: var(--success-700);
    border: 1px solid var(--success-300);
}

.status-cancelled {
    background: var(--error-100);
    color: var(--error-700);
    border: 1px solid var(--error-300);
}

/* Order Card Styles */
.order-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-4);
    border: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

.order-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-4);
}

.order-date {
    display: block;
    font-size: var(--text-sm);
    color: var(--neutral-500);
    margin-top: var(--space-1);
}

.order-details {
    margin-bottom: var(--space-4);
}

.order-details p {
    margin-bottom: var(--space-2);
    color: var(--neutral-700);
}

.order-actions {
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--neutral-600);
}

.empty-state i {
    font-size: 3rem;
    color: var(--neutral-400);
    margin-bottom: var(--space-4);
}

.empty-state h3 {
    font-size: var(--text-xl);
    color: var(--neutral-800);
    margin-bottom: var(--space-2);
}

.empty-state p {
    margin-bottom: var(--space-6);
}

/* Enhanced Customize Page Styles */
.customize-main {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-50), var(--accent-50), var(--neutral-50));
    padding-top: calc(var(--space-20) + var(--space-4));
    padding-bottom: var(--space-16);
}

.customize-hero {
    text-align: center;
    margin-bottom: var(--space-12);
}

.hero-title {
    font-family: var(--font-family-serif);
    font-size: var(--text-4xl);
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    background: linear-gradient(135deg, var(--primary-600), var(--accent-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--text-lg);
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto var(--space-8);
    line-height: 1.6;
}

/* Enhanced Progress Steps */
.progress-container {
    max-width: 800px;
    margin: 0 auto;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-6);
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.step-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: white;
    border: 3px solid var(--neutral-300);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-3);
    transition: var(--transition-normal);
    position: relative;
    z-index: 2;
}

.progress-step.active .step-circle {
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    border-color: var(--primary-500);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.progress-step.completed .step-circle {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
}

.step-circle i {
    font-size: var(--text-lg);
    opacity: 1;
}

.step-circle .step-number {
    font-size: var(--text-sm);
    font-weight: 600;
    display: none;
}

.progress-step.active .step-circle i {
    display: block;
}

.step-info {
    text-align: center;
}

.step-title {
    display: block;
    font-weight: 600;
    color: var(--neutral-800);
    font-size: var(--text-sm);
    margin-bottom: var(--space-1);
}

.step-desc {
    display: block;
    font-size: var(--text-xs);
    color: var(--neutral-500);
}

.progress-step.active .step-title {
    color: var(--primary-600);
}

.progress-line {
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--accent-600));
    border-radius: var(--radius-full);
    transition: width 0.5s ease;
}

/* Main Layout */
.customize-layout {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
}

/* Preview Panel */
.preview-panel {
    position: sticky;
    top: calc(var(--space-20) + var(--space-4));
    height: fit-content;
}

.preview-card {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    border: 1px solid var(--neutral-200);
}

.preview-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h2 {
    font-family: var(--font-family-serif);
    font-size: var(--text-2xl);
    color: var(--neutral-900);
    margin: 0;
}

.preview-badges {
    display: flex;
    gap: var(--space-2);
}

.badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.badge.premium {
    background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
    color: var(--warning-700);
    border: 1px solid var(--warning-300);
}

.badge.custom {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--primary-700);
    border: 1px solid var(--primary-300);
}

/* Dress Showcase */
.dress-showcase {
    padding: var(--space-6);
}

.dress-image-wrapper {
    position: relative;
    margin-bottom: var(--space-6);
    border-radius: var(--radius-xl);
    overflow: hidden;
    background: var(--neutral-50);
}

.dress-image-wrapper img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.image-overlay {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    opacity: 0;
    transition: var(--transition-normal);
}

.dress-image-wrapper:hover .image-overlay {
    opacity: 1;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.zoom-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.dress-details {
    text-align: center;
}

.dress-title {
    font-family: var(--font-family-serif);
    font-size: var(--text-2xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-3);
}

.dress-desc {
    color: var(--neutral-600);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

/* Customization Summary */
.customization-summary {
    background: var(--neutral-50);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    border: 1px solid var(--neutral-200);
}

.customization-summary h4 {
    font-size: var(--text-lg);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    text-align: center;
}

.selection-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.selection-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--neutral-200);
}

.selection-item:last-child {
    border-bottom: none;
}

.selection-label {
    font-weight: 500;
    color: var(--neutral-700);
}

.selection-value {
    color: var(--neutral-900);
    font-weight: 600;
}

/* Price Summary */
.price-summary {
    background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    border: 1px solid var(--primary-200);
}

.price-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--neutral-700);
}

.price-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--primary-700);
    padding-top: var(--space-3);
    border-top: 2px solid var(--primary-300);
    margin-top: var(--space-3);
}

/* Customization Panel */
.customization-panel {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid var(--neutral-200);
}

.form-step {
    display: none;
    padding: var(--space-8);
}

.form-step.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 2px solid var(--neutral-100);
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    flex-shrink: 0;
}

.step-content h3 {
    font-family: var(--font-family-serif);
    font-size: var(--text-2xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
}

.step-content p {
    color: var(--neutral-600);
    line-height: 1.6;
}

/* Fabric Grid */
.fabric-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.fabric-option {
    position: relative;
}

.fabric-card {
    background: white;
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.fabric-card:hover {
    border-color: var(--primary-300);
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.fabric-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
}

.fabric-option input[type="radio"]:checked + label.fabric-card {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 4px rgba(237, 125, 71, 0.2);
    transform: translateY(-2px);
}

.fabric-option input[type="radio"]:checked + label .fabric-selector {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.fabric-option input[type="radio"]:checked + label .fabric-selector i {
    opacity: 1;
    transform: scale(1);
}

.fabric-selector {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    width: 28px;
    height: 28px;
    border: 2px solid var(--neutral-300);
    border-radius: 50%;
    background: white;
    cursor: pointer;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.fabric-selector i {
    color: white;
    font-size: 12px;
    opacity: 0;
    transform: scale(0);
    transition: var(--transition-fast);
}

.fabric-image {
    height: 120px;
    overflow: hidden;
}

.fabric-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.fabric-card:hover .fabric-image img {
    transform: scale(1.1);
}

.fabric-info {
    padding: var(--space-4);
    text-align: center;
}

.fabric-info h4 {
    font-size: var(--text-lg);
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
    font-weight: 600;
}

.fabric-info p {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    margin-bottom: var(--space-3);
}

.fabric-price {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--primary-700);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
}

/* Fabric Details */
.fabric-details {
    margin-bottom: var(--space-8);
}

.details-card {
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    text-align: center;
}

.details-card h4 {
    font-size: var(--text-lg);
    color: var(--neutral-900);
    margin-bottom: var(--space-3);
}

.details-card p {
    color: var(--neutral-600);
    line-height: 1.6;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
    padding-top: var(--space-6);
    border-top: 1px solid var(--neutral-200);
}

.step-actions .btn {
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.step-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive Design for Customize Page */
@media (max-width: 1024px) {
    .customize-layout {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .preview-panel {
        position: static;
        order: 2;
    }

    .customization-panel {
        order: 1;
    }
}

@media (max-width: 768px) {
    .customize-main {
        padding-top: calc(var(--space-16) + var(--space-4));
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: var(--space-4);
    }

    .progress-step {
        flex: 0 0 calc(50% - var(--space-2));
    }

    .step-circle {
        width: 50px;
        height: 50px;
    }

    .fabric-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-4);
    }

    .step-header {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .step-actions {
        flex-direction: column;
        gap: var(--space-3);
    }

    .step-actions .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .form-step {
        padding: var(--space-6);
    }

    .dress-showcase {
        padding: var(--space-4);
    }

    .fabric-grid {
        grid-template-columns: 1fr;
    }

    .progress-step {
        flex: 0 0 100%;
    }
}

/* Fabric Properties and Details */
.fabric-properties {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    margin-top: var(--space-4);
    justify-content: center;
}

.property-tag {
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--primary-700);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
    border: 1px solid var(--primary-300);
}

/* Enhanced Details Card */
.details-card {
    transition: var(--transition-normal);
}

.details-card:hover {
    border-color: var(--primary-300);
    box-shadow: var(--shadow-sm);
}

/* Selection Updates */
.selection-value {
    transition: var(--transition-fast);
}

.selection-value:not(:empty) {
    color: var(--primary-600);
    font-weight: 600;
}

/* Color Selection Styles */
.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.color-option {
    position: relative;
}

.color-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
}

.color-card {
    background: white;
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    display: block;
    text-decoration: none;
    color: inherit;
}

.color-card:hover {
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.color-option input[type="radio"]:checked + .color-card {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 4px rgba(237, 125, 71, 0.2);
    transform: translateY(-2px);
}

.color-option input[type="radio"]:checked + .color-card .color-selector {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.color-option input[type="radio"]:checked + .color-card .color-selector i {
    opacity: 1;
    transform: scale(1);
}

.color-selector {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    width: 28px;
    height: 28px;
    border: 2px solid var(--neutral-300);
    border-radius: 50%;
    background: white;
    cursor: pointer;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.color-selector i {
    color: white;
    font-size: 12px;
    opacity: 0;
    transform: scale(0);
    transition: var(--transition-fast);
}

.color-swatch {
    height: 80px;
    width: 100%;
    border-bottom: 1px solid var(--neutral-200);
}

.color-info {
    padding: var(--space-4);
    text-align: center;
}

.color-info h4 {
    font-size: var(--text-base);
    color: var(--neutral-900);
    margin-bottom: var(--space-1);
    font-weight: 600;
}

.color-info p {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    margin: 0;
}

/* Progress Step States */
.progress-step.completed .step-circle {
    background: var(--success-500);
    border-color: var(--success-500);
    color: white;
}

.progress-step.completed .step-circle i {
    display: none;
}

.progress-step.completed .step-circle::after {
    content: '✓';
    font-size: var(--text-sm);
    font-weight: bold;
}

/* Responsive Color Grid */
@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: var(--space-3);
    }

    .color-swatch {
        height: 60px;
    }

    .color-info {
        padding: var(--space-3);
    }
}

/* Measurements Section */
.measurements-section {
    margin-bottom: var(--space-8);
}

.measurements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.measurement-item {
    display: flex;
    flex-direction: column;
}

.measurement-item label {
    font-weight: 500;
    color: var(--neutral-700);
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
}

.measurement-item input {
    padding: var(--space-3);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: var(--transition-fast);
}

.measurement-item input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(237, 125, 71, 0.1);
}

.measurements-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Delivery Section */
.delivery-section {
    margin-bottom: var(--space-8);
}

.delivery-section h4 {
    font-size: var(--text-lg);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    font-weight: 600;
}

.timeline-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.timeline-option {
    position: relative;
}

.timeline-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
}

.timeline-card {
    background: white;
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    cursor: pointer;
    transition: var(--transition-normal);
    display: block;
    text-decoration: none;
    color: inherit;
}

.timeline-card:hover {
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.timeline-option input[type="radio"]:checked + .timeline-card {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(237, 125, 71, 0.2);
    background: linear-gradient(135deg, var(--primary-50), white);
}

.timeline-info {
    text-align: center;
}

.timeline-info h5 {
    font-size: var(--text-base);
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
    font-weight: 600;
}

.timeline-info p {
    color: var(--neutral-600);
    font-size: var(--text-sm);
    margin-bottom: var(--space-3);
}

.timeline-price {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    color: var(--primary-700);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
}

/* Saved Addresses */
.saved-addresses {
    margin-bottom: var(--space-6);
}

.address-card {
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    cursor: pointer;
    transition: var(--transition-fast);
}

.address-card:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
}

.address-card.selected {
    border-color: var(--primary-500);
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
}

/* Responsive Measurements and Delivery */
@media (max-width: 768px) {
    .measurements-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-3);
    }

    .measurements-actions {
        flex-direction: column;
        align-items: center;
    }

    .measurements-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .timeline-options {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }
}

/* Message Notifications */
.message {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-500);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    min-width: 300px;
    max-width: 400px;
}

.message.success {
    border-left-color: var(--success-500);
    background: linear-gradient(135deg, var(--success-50), white);
}

.message.error {
    border-left-color: var(--error-500);
    background: linear-gradient(135deg, var(--error-50), white);
}

.message.info {
    border-left-color: var(--primary-500);
    background: linear-gradient(135deg, var(--primary-50), white);
}

.message i {
    font-size: var(--text-lg);
}

.message.success i {
    color: var(--success-500);
}

.message.error i {
    color: var(--error-500);
}

.message.info i {
    color: var(--primary-500);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* About Section */
.about-section {
    padding: 80px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-item i {
    font-size: 2rem;
    color: #ff6b9d;
    margin-top: 0.5rem;
}

.feature-item h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.contact-section h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.contact-item i {
    font-size: 1.5rem;
    color: #ff6b9d;
    margin-top: 0.5rem;
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #ff6b9d;
}

.submit-btn {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: linear-gradient(135deg, #c44569, #ff6b9d);
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ff6b9d;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ff6b9d;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    color: #bdc3c7;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #ff6b9d;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #7f8c8d;
}

.modal-content h2 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
    text-align: center;
}

.modal-content form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modal-content input {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
}

.modal-content input:focus {
    outline: none;
    border-color: #ff6b9d;
}

.modal-content button {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
}

.modal-content p {
    text-align: center;
    margin-top: 1rem;
}

.modal-content a {
    color: #ff6b9d;
    text-decoration: none;
}

/* Enhanced Mobile Navigation */
.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    left: -100%;
    width: 85%;
    max-width: 320px;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-500), var(--accent-600));
    z-index: 2000;
    transition: var(--transition-normal);
    padding: var(--space-6);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-2xl);
}

.mobile-nav.active {
    left: 0;
}

.mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    color: white;
}

.mobile-nav-close {
    font-size: 1.5rem;
    cursor: pointer;
}

.mobile-nav-links {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.mobile-nav-links a {
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    padding: 1rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.mobile-nav-links a:hover {
    background: rgba(255,255,255,0.2);
}

.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1500;
}

.mobile-overlay.active {
    display: block;
}

/* Enhanced Product Cards */
.dress-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
}

.dress-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.dress-image {
    height: 280px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.dress-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,107,157,0.1), rgba(196,69,105,0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dress-card:hover .dress-image::before {
    opacity: 1;
}

.quick-view-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255,255,255,0.9);
    color: #ff6b9d;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dress-card:hover .quick-view-btn {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
}

.wishlist-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255,255,255,0.9);
    color: #ff6b9d;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wishlist-btn:hover {
    background: #ff6b9d;
    color: white;
    transform: scale(1.1);
}

.dress-info {
    padding: 2rem;
}

.dress-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    color: #ffd700;
}

.rating-count {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Enhanced Filter System */
.filter-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.filter-row {
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.filter-select {
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #ff6b9d;
}

.price-range {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.price-slider {
    width: 200px;
    height: 6px;
    border-radius: 3px;
    background: #ecf0f1;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.price-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ff6b9d;
    cursor: pointer;
}

.sort-options {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.sort-btn {
    background: transparent;
    border: 2px solid #ecf0f1;
    color: #7f8c8d;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.sort-btn.active,
.sort-btn:hover {
    border-color: #ff6b9d;
    color: #ff6b9d;
    background: rgba(255,107,157,0.1);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content h1 {
        font-size: 3rem;
    }
    
    .dresses-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Enhanced Loading States */
.loading-dresses {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-16);
    color: var(--neutral-600);
}

.loading-spinner {
    margin-bottom: var(--space-4);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--neutral-200);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced No Results */
.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-16);
    text-align: center;
    grid-column: 1 / -1;
}

.no-results-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--neutral-100);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-6);
    color: var(--neutral-400);
    font-size: var(--text-3xl);
}

.no-results h3 {
    font-size: var(--text-2xl);
    color: var(--neutral-800);
    margin-bottom: var(--space-3);
    font-weight: 600;
}

.no-results p {
    color: var(--neutral-600);
    margin-bottom: var(--space-8);
    max-width: 400px;
}

.no-results-actions {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
    justify-content: center;
}

/* Enhanced Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.animate-in {
    opacity: 1;
    transform: translateY(0);
    transition: var(--transition-normal);
}

/* Message Container */
.message-container {
    position: fixed;
    top: var(--space-20);
    right: var(--space-6);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-width: 400px;
}

.message {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-500);
    padding: var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideInRight 0.3s ease-out;
}

.message-success {
    border-left-color: var(--success-500);
}

.message-error {
    border-left-color: var(--error-500);
}

.message-warning {
    border-left-color: var(--warning-500);
}

.message-info {
    border-left-color: var(--info-500);
}

.message-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.message-close {
    background: none;
    border: none;
    color: var(--neutral-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.message-close:hover {
    color: var(--neutral-600);
    background: var(--neutral-100);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex !important;
    }

    /* Mobile Hero Adjustments */
    .hero {
        padding: calc(var(--space-20) + var(--space-4)) 0 var(--space-16);
        min-height: 80vh;
    }

    .hero-content {
        text-align: center;
        margin-bottom: var(--space-8);
    }

    .hero-features {
        justify-content: center;
        gap: var(--space-3);
    }

    .feature {
        padding: var(--space-2) var(--space-4);
        font-size: var(--text-sm);
    }

    .hero-cta {
        justify-content: center;
        gap: var(--space-3);
    }

    .cta-button {
        padding: var(--space-3) var(--space-6);
        font-size: var(--text-base);
        min-width: 140px;
    }

    /* Mobile Dresses Grid */
    .dresses-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-6);
    }

    /* Mobile Filter Tabs */
    .filter-tabs {
        gap: var(--space-2);
        overflow-x: auto;
        padding-bottom: var(--space-2);
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .filter-tabs::-webkit-scrollbar {
        display: none;
    }

    .filter-btn {
        white-space: nowrap;
        min-width: auto;
        padding: var(--space-2) var(--space-4);
        font-size: var(--text-xs);
    }

    /* Mobile About Section */
    .about-content {
        flex-direction: column;
        gap: var(--space-8);
    }

    .about-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--space-4);
        padding: var(--space-6);
    }

    .stat-number {
        font-size: var(--text-3xl);
    }

    /* Mobile Forms */
    .form-row {
        flex-direction: column;
        gap: var(--space-4);
    }

    .measurements-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .style-grid,
    .fabric-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: var(--space-3);
    }

    /* Mobile Progress Steps */
    .progress-steps {
        flex-direction: column;
        gap: var(--space-2);
    }

    .progress-step {
        flex-direction: row;
        align-items: center;
        gap: var(--space-3);
    }

    .step-content {
        text-align: left;
    }

    /* Mobile Navigation */
    .step-navigation {
        flex-direction: column;
        gap: var(--space-3);
    }

    .step-navigation .btn {
        width: 100%;
        justify-content: center;
    }

    /* Mobile Message Container */
    .message-container {
        left: var(--space-4);
        right: var(--space-4);
        top: var(--space-16);
        max-width: none;
    }
}
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 120px 0 80px;
    }
    
    .hero-content {
        padding: 0 15px;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
        line-height: 1.1;
    }
    
    .hero-features {
        justify-content: center;
        gap: 1rem;
    }
    
    .feature {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .dresses-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .filter-section {
        padding: 1.5rem;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-select {
        width: 100%;
    }
    
    .price-slider {
        width: 100%;
    }
    
    .sort-options {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }
    
    .feature {
        width: 100%;
        justify-content: center;
        text-align: center;
    }
    
    .dresses-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dress-card {
        margin: 0 10px;
    }
    
    .dress-image {
        height: 250px;
    }
    
    .dress-info {
        padding: 1.5rem;
    }
    
    .modal-content {
        margin: 5% auto;
        width: 95%;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .customize-modal {
        padding: 1rem;
    }
    
    .customize-content {
        flex-direction: column;
    }
    
    .dress-preview {
        margin-bottom: 2rem;
    }
    
    .filter-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 1rem;
        gap: 0.5rem;
    }
    
    .filter-btn {
        white-space: nowrap;
        min-width: auto;
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
}

/* Results Info and View Toggle */
.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    background: transparent;
    border: 2px solid #ecf0f1;
    color: #7f8c8d;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    border-color: #ff6b9d;
    color: #ff6b9d;
    background: rgba(255,107,157,0.1);
}

/* OTP Authentication Styles */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.auth-step {
    margin-bottom: 1.5rem;
}

.phone-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.country-code {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    width: 100px;
    background: white;
}

.phone-input-group input {
    flex: 1;
    padding: 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
}

.phone-input-group input:focus,
.country-code:focus {
    outline: none;
    border-color: #ff6b9d;
}

.auth-btn {
    width: 100%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.auth-btn:hover {
    background: linear-gradient(135deg, #c44569, #ff6b9d);
    transform: translateY(-1px);
}

.auth-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.otp-input-group {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin: 1.5rem 0;
}

.otp-digit,
.reg-otp-digit {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.otp-digit:focus,
.reg-otp-digit:focus {
    outline: none;
    border-color: #ff6b9d;
    background: rgba(255,107,157,0.1);
}

.otp-digit.filled,
.reg-otp-digit.filled {
    border-color: #27ae60;
    background: rgba(39,174,96,0.1);
}

.otp-info {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.otp-info span {
    color: #2c3e50;
    font-weight: 600;
}

.otp-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.resend-btn {
    background: transparent;
    border: 2px solid #ecf0f1;
    color: #7f8c8d;
    padding: 0.8rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.resend-btn:hover:not(:disabled) {
    border-color: #ff6b9d;
    color: #ff6b9d;
}

.resend-btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.back-btn {
    background: transparent;
    border: none;
    color: #7f8c8d;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.back-btn:hover {
    color: #ff6b9d;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 0.2rem;
    transform: scale(1.2);
    accent-color: #ff6b9d;
}

.checkbox-group label {
    font-size: 0.9rem;
    color: #7f8c8d;
    line-height: 1.4;
}

.checkbox-group label a {
    color: #ff6b9d;
    text-decoration: none;
}

.checkbox-group label a:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
    margin-top: 1rem;
}

.auth-footer p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #ff6b9d;
    text-decoration: none;
    font-weight: 600;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message.success {
    background: rgba(39,174,96,0.1);
    color: #27ae60;
    border: 1px solid rgba(39,174,96,0.3);
}

.message.error {
    background: rgba(231,76,60,0.1);
    color: #e74c3c;
    border: 1px solid rgba(231,76,60,0.3);
}

.message.info {
    background: rgba(52,152,219,0.1);
    color: #3498db;
    border: 1px solid rgba(52,152,219,0.3);
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .dress-card:hover {
        transform: none;
    }
    
    .quick-view-btn {
        opacity: 1;
        position: static;
        transform: none;
        margin-top: 1rem;
        width: 100%;
    }
    
    .dress-card:hover .quick-view-btn {
        transform: none;
    }
    
    .otp-digit,
    .reg-otp-digit {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }
    
    .auth-btn,
    .resend-btn {
        padding: 1.2rem;
        font-size: 1.1rem;
    }
}

/* Profile Page Styles */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 40px;
}

.profile-header {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 600;
}

.profile-info h1 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.profile-info p {
    color: #7f8c8d;
    margin-bottom: 0.3rem;
}

.profile-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.profile-tab {
    padding: 0.8rem 1.5rem;
    border: none;
    background: transparent;
    color: #7f8c8d;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
}

.profile-tab.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
}

.profile-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    font-family: 'Playfair Display', serif;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #ecf0f1;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff6b9d;
}

.address-card,
.measurement-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.address-card h4,
.measurement-card h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.address-actions,
.measurement-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

.btn-edit,
.btn-delete {
    padding: 0.3rem 0.8rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #3498db;
    color: white;
}

.btn-delete {
    background: #e74c3c;
    color: white;
}

.btn-add {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1rem;
}

.btn-save {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    margin-top: 1rem;
}

.btn-save:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255,107,157,0.3);
}

/* Login/Register Page Styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    padding: 2rem;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    padding: 2.5rem;
}

.auth-card h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.auth-form .form-group {
    margin-bottom: 0;
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    color: #7f8c8d;
}

.auth-links a {
    color: #ff6b9d;
    text-decoration: none;
    font-weight: 600;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Responsive Profile Styles */
@media (max-width: 768px) {
    .profile-container {
        padding: 100px 15px 20px;
    }
    
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .address-card,
    .measurement-card {
        padding: 1rem;
    }
    
    .address-actions,
    .measurement-actions {
        position: static;
        margin-top: 1rem;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 2rem;
        margin: 1rem;
    }
    
    .profile-content {
        padding: 1.5rem;
    }
}

/* Final Accessibility and Performance Enhancements */

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-500: #d02d75;
        --accent-600: #b01e5e;
        --neutral-800: #000000;
        --neutral-600: #333333;
    }

    .btn, .filter-btn, .cta-button {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero-title-line,
    .hero-description,
    .hero-features,
    .hero-cta {
        opacity: 1;
        transform: none;
        animation: none;
    }
}

/* Focus Visible Support */
@supports selector(:focus-visible) {
    *:focus {
        outline: none;
    }

    *:focus-visible {
        outline: 2px solid var(--primary-500);
        outline-offset: 2px;
    }
}

/* Print Styles */
@media print {
    .header,
    .mobile-nav,
    .scroll-indicator,
    .message-container,
    .loading-skeleton {
        display: none !important;
    }

    .hero {
        padding-top: 0;
        min-height: auto;
    }

    .dress-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .cta-button,
    .btn {
        border: 1px solid #000;
        background: transparent !important;
        color: #000 !important;
    }
}

/* Performance Optimizations */
.dress-card img {
    content-visibility: auto;
    contain-intrinsic-size: 280px;
}

.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.loaded {
    opacity: 1;
}

/* Enhanced Focus Management */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-500);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
}

.skip-link:focus {
    top: 6px;
}

/* Improved Button States */
.btn:disabled,
.cta-button:disabled,
.filter-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:not(:disabled):hover,
.cta-button:not(:disabled):hover {
    transform: translateY(-2px);
}

.btn:not(:disabled):active,
.cta-button:not(:disabled):active {
    transform: translateY(0);
}

/* Enhanced Error States */
.error-message {
    color: var(--error-500);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: none;
}

.form-group.error .error-message {
    display: block;
}

.form-group.error input,
.form-group.error select {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success States */
.form-group.success input,
.form-group.success select {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}