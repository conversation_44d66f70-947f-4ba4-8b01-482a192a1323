const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderId: {
    type: String,
    unique: true,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Allow guest orders
  },
  customerName: String,
  customerEmail: String,
  customerPhone: String,

  // Support for multiple items in an order
  items: [{
    dressId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Dress',
      required: true
    },
    quantity: {
      type: Number,
      default: 1,
      min: 1
    },
    price: {
      type: Number,
      required: true
    },
    customization: {
      fabric: String,
      color: String,
      measurements: {
        bust: Number,
        waist: Number,
        hip: Number,
        length: Number,
        unit: { type: String, enum: ['inches', 'cm'], default: 'inches' }
      },
      specialInstructions: String
    }
  }],

  // Legacy fields for backward compatibility
  dressId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dress'
  },
  measurements: {
    bust: Number,
    waist: Number,
    hip: Number,
    length: Number,
    unit: { type: String, enum: ['inches', 'cm'], default: 'inches' }
  },
  customizations: {
    fabric: String,
    color: String,
    specialInstructions: String
  },

  deliveryOption: {
    type: String,
    enum: ['express', 'standard', 'regular']
  },
  deliveryAddress: String,
  shippingAddress: {
    streetAddress: String,
    city: String,
    state: String,
    zipCode: String,
    country: { type: String, default: 'India' }
  },
  addressId: { // Reference to saved address
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User.addresses'
  },
  measurementId: { // Reference to saved measurement
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User.measurements'
  },
  totalAmount: Number,
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'in-progress', 'ready', 'dispatched', 'delivered', 'cancelled'],
    default: 'pending'
  },
  porterOrderId: String,
  payment: {
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded'],
      default: 'pending'
    },
    method: String,
    transactionId: String,
    amount: Number
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  cancellation: {
    reason: String,
    cancelledAt: Date
  },
  notes: String,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update timestamp on save
orderSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for order summary
orderSchema.virtual('summary').get(function() {
  return {
    orderId: this.orderId,
    status: this.status,
    totalAmount: this.totalAmount,
    createdAt: this.createdAt,
    deliveryOption: this.deliveryOption
  };
});

module.exports = mongoose.model('Order', orderSchema);