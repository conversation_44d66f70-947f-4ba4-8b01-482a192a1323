// Test script to verify <PERSON><PERSON>pay integration
// Run this with: node test-razorpay.js

require('dotenv').config();
const Razorpay = require('razorpay');

console.log('Testing Razorpay Integration...\n');

// Check environment variables
console.log('1. Checking environment variables:');
console.log('   RAZORPAY_KEY_ID:', process.env.RAZORPAY_KEY_ID ? 'Set' : 'Not set');
console.log('   RAZORPAY_KEY_SECRET:', process.env.RAZORPAY_KEY_SECRET ? 'Set' : 'Not set');

if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    console.log('\n❌ Error: Razorpay credentials not found in .env file');
    console.log('Please add your Razorpay credentials to .env file:');
    console.log('RAZORPAY_KEY_ID=your_key_id_here');
    console.log('RAZORPAY_KEY_SECRET=your_key_secret_here');
    process.exit(1);
}

if (process.env.RAZORPAY_KEY_ID === 'your_razorpay_key_id_here' || 
    process.env.RAZORPAY_KEY_SECRET === 'your_razorpay_key_secret_here') {
    console.log('\n❌ Error: Please replace placeholder Razorpay credentials with real ones');
    console.log('Get your credentials from: https://dashboard.razorpay.com/');
    process.exit(1);
}

// Test Razorpay initialization
console.log('\n2. Testing Razorpay initialization:');
try {
    const razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET,
    });
    console.log('   ✅ Razorpay instance created successfully');

    // Test creating a test order
    console.log('\n3. Testing order creation:');
    const testOrder = {
        amount: 50000, // ₹500 in paise
        currency: 'INR',
        receipt: 'test_receipt_' + Date.now(),
        payment_capture: 1
    };

    razorpay.orders.create(testOrder)
        .then(order => {
            console.log('   ✅ Test order created successfully');
            console.log('   Order ID:', order.id);
            console.log('   Amount:', order.amount / 100, 'INR');
            console.log('   Status:', order.status);
            console.log('\n🎉 Razorpay integration is working correctly!');
            console.log('\nNext steps:');
            console.log('1. Start your server: npm start');
            console.log('2. Go to checkout page and test payment');
            console.log('3. Use test card: 4111 1111 1111 1111');
        })
        .catch(error => {
            console.log('   ❌ Failed to create test order');
            console.log('   Error:', error.message);
            
            if (error.statusCode === 401) {
                console.log('\n💡 This usually means your API credentials are incorrect.');
                console.log('   Please verify your Razorpay Key ID and Secret.');
            } else if (error.statusCode === 400) {
                console.log('\n💡 Bad request - check your account status and API version.');
            }
        });

} catch (error) {
    console.log('   ❌ Failed to initialize Razorpay');
    console.log('   Error:', error.message);
}
