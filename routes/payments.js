const express = require('express');
const router = express.Router();
const Razorpay = require('razorpay');
const crypto = require('crypto');
const Order = require('../models/Order');
const { authenticate } = require('../middleware/auth');

// Initialize Razorpay with environment variables
let razorpay;
try {
    if (process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET) {
        razorpay = new Razorpay({
            key_id: process.env.RAZORPAY_KEY_ID,
            key_secret: process.env.RAZORPAY_KEY_SECRET
        });
    } else {
        console.warn('Razorpay credentials not found. Using mock payment service.');
        razorpay = null;
    }
} catch (error) {
    console.warn('Failed to initialize Razorpay:', error.message);
    razorpay = null;
}

// Mock payment service for development/testing
const mockPaymentService = {
    async orders() {
        return {
            create: async (options) => ({
                id: 'mock_order_' + Date.now(),
                amount: options.amount,
                currency: options.currency,
                receipt: options.receipt,
                status: 'created'
            })
        };
    },
    async payments() {
        return {
            refund: async (paymentId, options) => ({
                id: 'mock_refund_' + Date.now(),
                amount: options.amount || 1000,
                status: 'processed'
            })
        };
    }
};

// Use mock service if Razorpay not configured
const paymentService = razorpay || mockPaymentService;

// Create payment order
router.post('/create-order', authenticate, async (req, res) => {
    try {
        const { orderId } = req.body;
        
        // Find the order
        const order = await Order.findById(orderId).populate('userId');
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Verify order belongs to user
        if (order.userId._id.toString() !== req.user.userId) {
            return res.status(403).json({ message: 'Unauthorized' });
        }

        // Create payment order
        let razorpayOrder;
        if (razorpay) {
            razorpayOrder = await razorpay.orders.create({
                amount: Math.round(order.totalAmount * 100), // Convert to paise
                currency: 'INR',
                receipt: order.orderId,
                payment_capture: 1
            });
        } else {
            // Mock order for development
            razorpayOrder = {
                id: 'mock_order_' + Date.now(),
                amount: Math.round(order.totalAmount * 100),
                currency: 'INR',
                receipt: order.orderId,
                status: 'created'
            };
        }

        res.json({
            order: {
                id: razorpayOrder.id,
                amount: razorpayOrder.amount,
                currency: razorpayOrder.currency,
                key: process.env.RAZORPAY_KEY_ID || 'mock_key'
            }
        });

    } catch (error) {
        console.error('Error creating payment order:', error);
        res.status(500).json({ message: 'Failed to create payment order' });
    }
});

// Verify payment
router.post('/verify-payment', authenticate, async (req, res) => {
    try {
        const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;

        // Skip signature verification for mock payments
        if (razorpay) {
            // Verify signature
            const body = razorpay_order_id + '|' + razorpay_payment_id;
            const expectedSignature = crypto
                .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
                .update(body.toString())
                .digest('hex');

            if (expectedSignature !== razorpay_signature) {
                return res.status(400).json({ message: 'Invalid signature' });
            }
        }

        // Find order by Razorpay order ID
        const order = await Order.findOne({ 
            'payment.razorpayOrderId': razorpay_order_id,
            userId: req.user.userId
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Update order with payment details
        order.payment = {
            razorpayOrderId: razorpay_order_id,
            razorpayPaymentId: razorpay_payment_id,
            razorpaySignature: razorpay_signature,
            status: 'completed',
            amount: order.totalAmount,
            currency: 'INR',
            method: razorpay ? 'razorpay' : 'mock',
            paidAt: new Date()
        };

        order.status = 'confirmed';
        await order.save();

        res.json({ 
            success: true, 
            order: {
                _id: order._id,
                orderId: order.orderId,
                totalAmount: order.totalAmount,
                status: order.status,
                createdAt: order.createdAt
            }
        });

    } catch (error) {
        console.error('Error verifying payment:', error);
        res.status(500).json({ message: 'Payment verification failed' });
    }
});

// Get payment status
router.get('/status/:orderId', authenticate, async (req, res) => {
    try {
        const { orderId } = req.params;
        
        const order = await Order.findById(orderId);
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Verify order belongs to user
        if (order.userId.toString() !== req.user.userId) {
            return res.status(403).json({ message: 'Unauthorized' });
        }

        res.json({
            orderId: order.orderId,
            paymentStatus: order.payment?.status || 'pending',
            paymentDetails: order.payment
        });

    } catch (error) {
        console.error('Error getting payment status:', error);
        res.status(500).json({ message: 'Failed to get payment status' });
    }
});

// Refund payment
router.post('/refund/:orderId', authenticate, async (req, res) => {
    try {
        const { orderId } = req.params;
        const { amount, reason } = req.body;

        const order = await Order.findById(orderId);
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Verify order belongs to user
        if (order.userId.toString() !== req.user.userId) {
            return res.status(403).json({ message: 'Unauthorized' });
        }

        if (!order.payment?.razorpayPaymentId) {
            return res.status(400).json({ message: 'No payment found for this order' });
        }

        let refund;
        if (razorpay) {
            // Create real refund
            refund = await razorpay.payments.refund(order.payment.razorpayPaymentId, {
                amount: amount ? Math.round(amount * 100) : undefined,
                notes: {
                    reason: reason || 'Customer requested refund'
                }
            });
        } else {
            // Mock refund for development
            refund = {
                id: 'mock_refund_' + Date.now(),
                amount: amount ? Math.round(amount * 100) : Math.round(order.totalAmount * 100),
                status: 'processed'
            };
        }

        // Update order
        order.payment.refund = {
            refundId: refund.id,
            amount: refund.amount / 100,
            status: refund.status,
            reason: reason || 'Customer requested refund',
            createdAt: new Date()
        };

        order.status = 'refunded';
        await order.save();

        res.json({
            success: true,
            refund: {
                refundId: refund.id,
                amount: refund.amount / 100,
                status: refund.status
            }
        });

    } catch (error) {
        console.error('Error processing refund:', error);
        res.status(500).json({ message: 'Refund processing failed' });
    }
});

module.exports = router;