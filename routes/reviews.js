const express = require('express');
const router = express.Router();
const Review = require('../models/Review');
const Order = require('../models/Order');
const Dress = require('../models/Dress');
const { authenticate } = require('../middleware/auth');

// Create a review
router.post('/', authenticate, async (req, res) => {
    try {
        const { dressId, orderId, rating, title, comment, images } = req.body;

        // Verify order belongs to user and is delivered
        const order = await Order.findOne({
            _id: orderId,
            userId: req.user._id,
            status: 'delivered'
        });

        if (!order) {
            return res.status(400).json({ 
                message: 'Order not found or not delivered' 
            });
        }

        // Check if dress exists in the order
        const dressInOrder = order.items.some(item => 
            item.dressId.toString() === dressId
        );

        if (!dressInOrder) {
            return res.status(400).json({ 
                message: 'Dress not found in this order' 
            });
        }

        // Check if user already reviewed this dress
        const existingReview = await Review.findOne({
            userId: req.user._id,
            dressId: dressId
        });

        if (existingReview) {
            return res.status(400).json({ 
                message: 'You have already reviewed this dress' 
            });
        }

        const review = new Review({
            userId: req.user.userId,
            dressId,
            orderId,
            rating,
            title,
            comment,
            images: images || []
        });

        await review.save();

        // Update dress rating
        await this.updateDressRating(dressId);

        res.status(201).json({ 
            message: 'Review submitted successfully',
            review 
        });

    } catch (error) {
        console.error('Error creating review:', error);
        res.status(500).json({ message: 'Failed to submit review' });
    }
});

// Get reviews for a dress
router.get('/dress/:dressId', async (req, res) => {
    try {
        const { dressId } = req.params;
        const { page = 1, limit = 10, sort = 'newest' } = req.query;

        let sortOption = {};
        switch (sort) {
            case 'newest':
                sortOption = { createdAt: -1 };
                break;
            case 'oldest':
                sortOption = { createdAt: 1 };
                break;
            case 'highest':
                sortOption = { rating: -1 };
                break;
            case 'lowest':
                sortOption = { rating: 1 };
                break;
            case 'helpful':
                sortOption = { helpful: -1 };
                break;
        }

        const reviews = await Review.find({ dressId })
            .populate('userId', 'name')
            .sort(sortOption)
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Review.countDocuments({ dressId });
        const averageRating = await Review.aggregate([
            { $match: { dressId: mongoose.Types.ObjectId(dressId) } },
            { $group: { _id: null, avgRating: { $avg: '$rating' } } }
        ]);

        res.json({
            reviews,
            totalPages: Math.ceil(total / limit),
            currentPage: page,
            total,
            averageRating: averageRating[0]?.avgRating || 0
        });

    } catch (error) {
        console.error('Error fetching reviews:', error);
        res.status(500).json({ message: 'Failed to fetch reviews' });
    }
});

// Get user's reviews
router.get('/my-reviews', authenticate, async (req, res) => {
    try {
        const reviews = await Review.find({ userId: req.user.userId })
            .populate('dressId', 'name images')
            .populate('orderId', 'orderId')
            .sort({ createdAt: -1 });

        res.json({ reviews });

    } catch (error) {
        console.error('Error fetching user reviews:', error);
        res.status(500).json({ message: 'Failed to fetch reviews' });
    }
});

// Update review
router.patch('/:reviewId', authenticate, async (req, res) => {
    try {
        const { reviewId } = req.params;
        const { rating, title, comment, images } = req.body;

        const review = await Review.findOne({
            _id: reviewId,
            userId: req.user.userId
        });

        if (!review) {
            return res.status(404).json({ message: 'Review not found' });
        }

        review.rating = rating || review.rating;
        review.title = title || review.title;
        review.comment = comment || review.comment;
        review.images = images || review.images;

        await review.save();

        // Update dress rating
        await this.updateDressRating(review.dressId);

        res.json({ 
            message: 'Review updated successfully',
            review 
        });

    } catch (error) {
        console.error('Error updating review:', error);
        res.status(500).json({ message: 'Failed to update review' });
    }
});

// Delete review
router.delete('/:reviewId', authenticate, async (req, res) => {
    try {
        const { reviewId } = req.params;

        const review = await Review.findOne({
            _id: reviewId,
            userId: req.user.userId
        });

        if (!review) {
            return res.status(404).json({ message: 'Review not found' });
        }

        const dressId = review.dressId;
        await Review.findByIdAndDelete(reviewId);

        // Update dress rating
        await this.updateDressRating(dressId);

        res.json({ message: 'Review deleted successfully' });

    } catch (error) {
        console.error('Error deleting review:', error);
        res.status(500).json({ message: 'Failed to delete review' });
    }
});

// Mark review as helpful
router.post('/:reviewId/helpful', authenticate, async (req, res) => {
    try {
        const { reviewId } = req.params;

        const review = await Review.findByIdAndUpdate(
            reviewId,
            { $inc: { helpful: 1 } },
            { new: true }
        );

        res.json({ 
            message: 'Marked as helpful',
            helpful: review.helpful 
        });

    } catch (error) {
        console.error('Error marking review helpful:', error);
        res.status(500).json({ message: 'Failed to mark as helpful' });
    }
});

// Report review
router.post('/:reviewId/report', authenticate, async (req, res) => {
    try {
        const { reviewId } = req.params;
        const { reason } = req.body;

        await Review.findByIdAndUpdate(
            reviewId,
            { 
                reported: true,
                reportReason: reason 
            }
        );

        res.json({ message: 'Review reported successfully' });

    } catch (error) {
        console.error('Error reporting review:', error);
        res.status(500).json({ message: 'Failed to report review' });
    }
});

// Helper function to update dress rating
async function updateDressRating(dressId) {
    const stats = await Review.aggregate([
        { $match: { dressId: mongoose.Types.ObjectId(dressId) } },
        {
            $group: {
                _id: null,
                avgRating: { $avg: '$rating' },
                totalReviews: { $sum: 1 }
            }
        }
    ]);

    if (stats.length > 0) {
        await Dress.findByIdAndUpdate(dressId, {
            averageRating: stats[0].avgRating,
            totalReviews: stats[0].totalReviews
        });
    } else {
        await Dress.findByIdAndUpdate(dressId, {
            averageRating: 0,
            totalReviews: 0
        });
    }
}

module.exports = router;