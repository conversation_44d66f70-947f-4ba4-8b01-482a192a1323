const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const { authenticate } = require('../middleware/auth');

// Get user's orders
router.get('/my-orders', authenticate, async (req, res) => {
    try {
        const orders = await Order.find({ userId: req.user._id })
            .populate('items.dressId', 'name images price')
            .sort({ createdAt: -1 });

        res.json({ orders });
    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({ message: 'Failed to fetch orders' });
    }
});

// Get order by ID
router.get('/:orderId', authenticate, async (req, res) => {
    try {
        const { orderId } = req.params;
        
        const order = await Order.findById(orderId)
            .populate('userId', 'name email phone')
            .populate('items.dressId', 'name images price');

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Verify order belongs to user
        if (order.userId._id.toString() !== req.user._id.toString()) {
            return res.status(403).json({ message: 'Unauthorized' });
        }

        res.json({ order });
    } catch (error) {
        console.error('Error fetching order:', error);
        res.status(500).json({ message: 'Failed to fetch order' });
    }
});

// Create new order
router.post('/', authenticate, async (req, res) => {
    try {
        const { items, customerInfo, totalAmount, shippingAddress, notes } = req.body;

        // Generate unique order ID
        const orderId = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();

        const order = new Order({
            orderId,
            userId: req.user._id,
            items: items.map(item => ({
                dressId: item.id || item._id,
                quantity: item.quantity,
                price: item.price,
                customization: item.customization || null
            })),
            totalAmount,
            shippingAddress,
            notes,
            status: 'pending',
            payment: {
                status: 'pending'
            }
        });

        await order.save();

        // Populate order with dress details
        const populatedOrder = await Order.findById(order._id)
            .populate('items.dressId', 'name images price');

        res.status(201).json({ 
            order: populatedOrder,
            message: 'Order created successfully' 
        });

    } catch (error) {
        console.error('Error creating order:', error);
        res.status(500).json({ message: 'Failed to create order' });
    }
});

// Cancel order
router.post('/:orderId/cancel', authenticate, async (req, res) => {
    try {
        const { orderId } = req.params;
        const { reason } = req.body;

        const order = await Order.findById(orderId);
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Verify order belongs to user
        if (order.userId.toString() !== req.user._id.toString()) {
            return res.status(403).json({ message: 'Unauthorized' });
        }

        // Check if order can be cancelled
        if (!['pending', 'confirmed'].includes(order.status)) {
            return res.status(400).json({ 
                message: 'Order cannot be cancelled at this stage' 
            });
        }

        order.status = 'cancelled';
        order.cancellation = {
            reason: reason || 'Customer requested cancellation',
            cancelledAt: new Date()
        };

        await order.save();

        res.json({ 
            message: 'Order cancelled successfully',
            order: {
                _id: order._id,
                status: order.status
            }
        });

    } catch (error) {
        console.error('Error cancelling order:', error);
        res.status(500).json({ message: 'Failed to cancel order' });
    }
});

// Update order status (for staff/admin)
router.patch('/:orderId/status', authenticate, async (req, res) => {
    try {
        const { orderId } = req.params;
        const { status, notes } = req.body;

        const order = await Order.findById(orderId);
        
        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        order.status = status;
        order.notes = notes || order.notes;

        await order.save();

        res.json({ 
            message: 'Order status updated successfully',
            order: {
                _id: order._id,
                status: order.status
            }
        });

    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({ message: 'Failed to update order status' });
    }
});

module.exports = router;