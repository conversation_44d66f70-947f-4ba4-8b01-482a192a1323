const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { generateToken, authenticate } = require('../middleware/auth');

// Customer Registration
router.post('/register', async (req, res) => {
  try {
    const { email, password, fullName, phoneNumber } = req.body;

    // Validation
    if (!email || !password || !fullName || !phoneNumber) {
      return res.status(400).json({ 
        error: 'Please provide all required fields: email, password, fullName, phoneNumber' 
      });
    }

    // Check if user exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Create new user
    const user = new User({
      email,
      password,
      profile: {
        fullName,
        phoneNumber
      }
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Send response without password
    const userResponse = {
      id: user._id,
      email: user.email,
      role: user.role,
      profile: user.profile,
      addresses: user.addresses,
      measurements: user.measurements
    };

    res.status(201).json({
      success: true,
      token,
      user: userResponse
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error during registration' });
  }
});

// Validate token endpoint
router.get('/validate', authenticate, async (req, res) => {
  try {
    // If we reach here, the token is valid (authenticate middleware passed)
    const user = {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role,
      profile: req.user.profile
    };

    res.json({
      success: true,
      user: user,
      message: 'Token is valid'
    });
  } catch (error) {
    console.error('Token validation error:', error);
    res.status(500).json({ error: 'Server error during token validation' });
  }
});

// Customer Login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({ 
        error: 'Please provide email and password' 
      });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Send response without password
    const userResponse = {
      id: user._id,
      email: user.email,
      role: user.role,
      profile: user.profile,
      addresses: user.addresses,
      measurements: user.measurements
    };

    res.json({
      success: true,
      token,
      user: userResponse
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error during login' });
  }
});

// Get current user profile
router.get('/profile', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');
    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Server error fetching profile' });
  }
});

// Update user profile
router.put('/profile', authenticate, async (req, res) => {
  try {
    const { profile } = req.body;
    
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update profile fields
    if (profile) {
      user.profile = { ...user.profile, ...profile };
    }

    await user.save();

    // Return updated user without password
    const userResponse = await User.findById(user._id).select('-password');
    
    res.json({
      success: true,
      user: userResponse
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Server error updating profile' });
  }
});

// Get user addresses
router.get('/addresses', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('addresses');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      addresses: user.addresses
    });
  } catch (error) {
    console.error('Get addresses error:', error);
    res.status(500).json({ error: 'Server error fetching addresses' });
  }
});

// Add new address
router.post('/addresses', authenticate, async (req, res) => {
  try {
    const { label, streetAddress, city, state, zipCode, isDefault } = req.body;

    // Validation
    if (!streetAddress || !city || !state || !zipCode) {
      return res.status(400).json({ 
        error: 'Please provide all required address fields' 
      });
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const newAddress = {
      label: label || 'Home',
      streetAddress,
      city,
      state,
      zipCode,
      isDefault: isDefault || false
    };

    // If this is the default address, unset others
    if (newAddress.isDefault) {
      user.addresses.forEach(addr => addr.isDefault = false);
    }

    user.addresses.push(newAddress);
    await user.save();

    res.json({
      success: true,
      addresses: user.addresses
    });
  } catch (error) {
    console.error('Add address error:', error);
    res.status(500).json({ error: 'Server error adding address' });
  }
});

// Update address
router.put('/addresses/:addressId', authenticate, async (req, res) => {
  try {
    const { label, streetAddress, city, state, zipCode, isDefault } = req.body;
    const { addressId } = req.params;

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const address = user.addresses.id(addressId);
    if (!address) {
      return res.status(404).json({ error: 'Address not found' });
    }

    // Update address fields
    if (label !== undefined) address.label = label;
    if (streetAddress !== undefined) address.streetAddress = streetAddress;
    if (city !== undefined) address.city = city;
    if (state !== undefined) address.state = state;
    if (zipCode !== undefined) address.zipCode = zipCode;
    
    if (isDefault !== undefined) {
      if (isDefault) {
        user.addresses.forEach(addr => addr.isDefault = false);
      }
      address.isDefault = isDefault;
    }

    await user.save();

    res.json({
      success: true,
      addresses: user.addresses
    });
  } catch (error) {
    console.error('Update address error:', error);
    res.status(500).json({ error: 'Server error updating address' });
  }
});

// Delete address
router.delete('/addresses/:addressId', authenticate, async (req, res) => {
  try {
    const { addressId } = req.params;

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    user.addresses.id(addressId).remove();
    await user.save();

    res.json({
      success: true,
      addresses: user.addresses
    });
  } catch (error) {
    console.error('Delete address error:', error);
    res.status(500).json({ error: 'Server error deleting address' });
  }
});

// Get user measurements
router.get('/measurements', authenticate, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('measurements');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      success: true,
      measurements: user.measurements
    });
  } catch (error) {
    console.error('Get measurements error:', error);
    res.status(500).json({ error: 'Server error fetching measurements' });
  }
});

// Add new measurement
router.post('/measurements', authenticate, async (req, res) => {
  try {
    const { label, bust, waist, hip, length, unit, isDefault } = req.body;

    // Validation
    if (!bust || !waist || !hip || !length) {
      return res.status(400).json({ 
        error: 'Please provide all required measurement fields' 
      });
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const newMeasurement = {
      label: label || 'Standard Size',
      bust: parseFloat(bust),
      waist: parseFloat(waist),
      hip: parseFloat(hip),
      length: parseFloat(length),
      unit: unit || 'inches',
      isDefault: isDefault || false
    };

    // If this is the default measurement, unset others
    if (newMeasurement.isDefault) {
      user.measurements.forEach(m => m.isDefault = false);
    }

    user.measurements.push(newMeasurement);
    await user.save();

    res.json({
      success: true,
      measurements: user.measurements
    });
  } catch (error) {
    console.error('Add measurement error:', error);
    res.status(500).json({ error: 'Server error adding measurement' });
  }
});

// Update measurement
router.put('/measurements/:measurementId', authenticate, async (req, res) => {
  try {
    const { label, bust, waist, hip, length, unit, isDefault } = req.body;
    const { measurementId } = req.params;

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const measurement = user.measurements.id(measurementId);
    if (!measurement) {
      return res.status(404).json({ error: 'Measurement not found' });
    }

    // Update measurement fields
    if (label !== undefined) measurement.label = label;
    if (bust !== undefined) measurement.bust = parseFloat(bust);
    if (waist !== undefined) measurement.waist = parseFloat(waist);
    if (hip !== undefined) measurement.hip = parseFloat(hip);
    if (length !== undefined) measurement.length = parseFloat(length);
    if (unit !== undefined) measurement.unit = unit;
    
    if (isDefault !== undefined) {
      if (isDefault) {
        user.measurements.forEach(m => m.isDefault = false);
      }
      measurement.isDefault = isDefault;
    }

    await user.save();

    res.json({
      success: true,
      measurements: user.measurements
    });
  } catch (error) {
    console.error('Update measurement error:', error);
    res.status(500).json({ error: 'Server error updating measurement' });
  }
});

// Delete measurement
router.delete('/measurements/:measurementId', authenticate, async (req, res) => {
  try {
    const { measurementId } = req.params;

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    user.measurements.id(measurementId).remove();
    await user.save();

    res.json({
      success: true,
      measurements: user.measurements
    });
  } catch (error) {
    console.error('Delete measurement error:', error);
    res.status(500).json({ error: 'Server error deleting measurement' });
  }
});

// Staff Registration
router.post('/staff/register', async (req, res) => {
  try {
    const { email, password, name, phone, role, adminCode } = req.body;

    // Validation
    if (!email || !password || !name || !phone || !role) {
      return res.status(400).json({
        error: 'Please provide all required fields: email, password, name, phone, role'
      });
    }

    // Validate admin code for admin role
    if (role === 'admin' && adminCode !== 'ADMIN2024') {
      return res.status(400).json({ error: 'Invalid admin authorization code' });
    }

    // Check if user exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Create new staff user
    const user = new User({
      email,
      password,
      role,
      profile: {
        fullName: name,
        phoneNumber: phone
      }
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Send response without password
    const userResponse = {
      id: user._id,
      email: user.email,
      role: user.role,
      profile: user.profile
    };

    res.status(201).json({
      success: true,
      token,
      user: userResponse
    });
  } catch (error) {
    console.error('Staff registration error:', error);
    res.status(500).json({ error: 'Server error during registration' });
  }
});

// Staff Login
router.post('/staff/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        error: 'Please provide email and password'
      });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Check if user is staff or admin
    if (user.role !== 'staff' && user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied. Staff only.' });
    }

    // Check password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Send response without password
    const userResponse = {
      id: user._id,
      email: user.email,
      role: user.role,
      profile: user.profile
    };

    res.json({
      success: true,
      token,
      user: userResponse
    });
  } catch (error) {
    console.error('Staff login error:', error);
    res.status(500).json({ error: 'Server error during login' });
  }
});

// Forgot Password
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    // Validation
    if (!email) {
      return res.status(400).json({ error: 'Please provide email address' });
    }

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ error: 'User not found with this email' });
    }

    // Generate reset token
    const crypto = require('crypto');
    const resetToken = crypto.randomBytes(32).toString('hex');
    
    // Hash token and set expiration (1 hour)
    user.passwordResetToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');
    user.passwordResetExpires = Date.now() + 3600000; // 1 hour

    await user.save();

    // In a real application, you would send an email here
    // For demo purposes, we'll return the reset URL
    const resetUrl = `${req.protocol}://${req.get('host')}/reset-password.html?token=${resetToken}`;

    res.json({
      success: true,
      message: 'Password reset email sent',
      resetUrl // Remove this in production - only for demo
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ error: 'Server error processing request' });
  }
});

// Reset Password
router.post('/reset-password', async (req, res) => {
  try {
    const { token, password } = req.body;

    // Validation
    if (!token || !password) {
      return res.status(400).json({ error: 'Please provide token and new password' });
    }

    // Hash token to match stored token
    const crypto = require('crypto');
    const hashedToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find user with valid token
    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({ error: 'Invalid or expired reset token' });
    }

    // Update password and clear reset fields
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    await user.save();

    res.json({
      success: true,
      message: 'Password reset successful'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ error: 'Server error resetting password' });
  }
});

// Change Password (authenticated)
router.post('/change-password', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validation
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Please provide current and new password' });
    }

    // Find user
    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ error: 'Server error changing password' });
  }
});

module.exports = router;